import asyncio
import sys
from typing import List

from app.config import config
from app.llm import LLM
from app.logger import logger
from app.schema import Message


class ManusLite:
    """A Python 3.9 compatible version of Manus without browser and advanced tool dependencies."""
    
    def __init__(self):
        self.llm = LLM()
        self.conversation_history: List[Message] = []
        self.max_steps = 10
        self.current_step = 0
    
    async def run(self, prompt: str):
        """Run the agent with a given prompt."""
        try:
            self.current_step = 0
            
            # Add user message to conversation
            user_message = Message(role="user", content=prompt)
            self.conversation_history.append(user_message)
            
            # Convert to OpenAI format
            messages = [{"role": msg.role, "content": msg.content} for msg in self.conversation_history]
            
            # Add system message with basic capabilities
            system_message = {
                "role": "system", 
                "content": """You are <PERSON><PERSON>, a helpful AI assistant. You can help with various tasks including:
- Answering questions and providing information
- Writing and explaining code
- Problem solving and analysis
- Creative writing and brainstorming
- Mathematical calculations

Note: This is a simplified version running on Python 3.9. Browser automation and file system tools are not available in this version."""
            }
            messages.insert(0, system_message)
            
            logger.info("Processing request with Manus Lite...")
            
            # Get response from LLM
            response = await self.llm.ask(messages, timeout=60)
            
            if response and hasattr(response, 'content'):
                assistant_message = Message(role="assistant", content=response.content)
                self.conversation_history.append(assistant_message)
                
                logger.info("Response generated successfully")
                print(f"\n🤖 Manus: {response.content}")
                
                # Check if the response indicates the task is complete
                if any(phrase in response.content.lower() for phrase in ["task completed", "finished", "done"]):
                    logger.info("Task appears to be completed")
                
            else:
                logger.warning("No response received from LLM")
                print("\n🤖 Manus: I'm sorry, I couldn't process your request at the moment.")
                
        except Exception as e:
            logger.error(f"Error processing request: {e}")
            print(f"\n❌ Error: {e}")
            
            # Provide helpful error messages
            if "authentication" in str(e).lower() or "api_key" in str(e).lower():
                print("💡 This appears to be an API key issue. Please check your configuration.")
            elif "rate limit" in str(e).lower():
                print("💡 Rate limit exceeded. Please wait a moment before trying again.")
            elif "network" in str(e).lower() or "connection" in str(e).lower():
                print("💡 Network connectivity issue. Please check your internet connection.")
    
    async def cleanup(self):
        """Cleanup resources."""
        logger.info("Cleaning up Manus Lite resources...")


async def main():
    """Main function - Python 3.9 compatible version of OpenManus."""
    print("=" * 70)
    print("🚀 OpenManus Lite - Python 3.9 Compatible Version")
    print("=" * 70)
    print(f"Python version: {sys.version}")
    print()
    
    # Check configuration
    try:
        api_key = config.llm['default'].api_key
        model = config.llm['default'].model
        
        print(f"📋 Configuration:")
        print(f"   Model: {model}")
        print(f"   API Key: {'✅ Set' if api_key and api_key not in ['demo-key', 'YOUR_API_KEY'] else '❌ Not set (using placeholder)'}")
        print()
        
        if api_key in ["demo-key", "YOUR_API_KEY"]:
            print("⚠️  To enable LLM functionality:")
            print("   1. Edit config/config.toml")
            print("   2. Add your actual OpenAI API key")
            print("   3. Restart the application")
            print()
            
    except Exception as e:
        logger.error(f"Configuration error: {e}")
        print(f"❌ Configuration error: {e}")
        return
    
    # Create and initialize Manus agent
    agent = ManusLite()
    
    try:
        prompt = input("Enter your prompt: ")
        if not prompt.strip():
            logger.warning("Empty prompt provided.")
            return

        logger.warning("Processing your request...")
        await agent.run(prompt)
        logger.info("Request processing completed.")
        
    except KeyboardInterrupt:
        logger.warning("Operation interrupted.")
        print("\n⚠️  Operation interrupted by user.")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        print(f"\n❌ Unexpected error: {e}")
    finally:
        # Ensure agent resources are cleaned up before exiting
        await agent.cleanup()
        print("\n👋 Goodbye!")


if __name__ == "__main__":
    asyncio.run(main())
