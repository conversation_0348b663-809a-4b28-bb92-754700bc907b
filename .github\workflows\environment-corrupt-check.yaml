name: Environment Corruption Check
on:
  push:
    branches: ["main"]
    paths:
      - requirements.txt
  pull_request:
    branches: ["main"]
    paths:
      - requirements.txt
concurrency:
  group: ${{ github.workflow }}-${{ github.event_name }}-${{ github.ref }}
  cancel-in-progress: true
jobs:
  test-python-versions:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.11.11", "3.12.8", "3.13.2"]
      fail-fast: false
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}
      - name: Upgrade pip
        run: |
          python -m pip install --upgrade pip
      - name: Install dependencies
        run: |
          pip install -r requirements.txt
