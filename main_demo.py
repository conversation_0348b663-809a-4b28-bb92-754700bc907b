import asyncio
import sys
from typing import List

from app.config import config
from app.logger import logger
from app.schema import Message


class MockResponse:
    """Mock response for demo purposes."""
    def __init__(self, content: str):
        self.content = content


class ManusDemo:
    """A demo version of Manus that works without API keys."""
    
    def __init__(self):
        self.conversation_history: List[Message] = []
        self.max_steps = 10
        self.current_step = 0
    
    async def mock_llm_response(self, messages: List[dict]) -> MockResponse:
        """Generate a mock response based on the user's input."""
        user_content = ""
        for msg in messages:
            if msg.get("role") == "user":
                user_content = msg.get("content", "").lower()
        
        # Simple response logic for demo
        if "hello" in user_content or "hi" in user_content:
            return MockResponse("Hello! I'm <PERSON><PERSON>, your AI assistant. How can I help you today?")
        elif "how are you" in user_content:
            return MockResponse("I'm doing well, thank you for asking! I'm ready to help you with various tasks.")
        elif "what can you do" in user_content:
            return MockResponse("""I can help you with many tasks including:
• Answering questions and providing information
• Writing and explaining code
• Problem solving and analysis
• Creative writing and brainstorming
• Mathematical calculations

Note: This is a demo version. For full functionality, please add a real API key to the configuration.""")
        elif "code" in user_content:
            return MockResponse("""I can help with coding! For example, here's a simple Python function:

```python
def greet(name):
    return f"Hello, {name}!"

# Usage
print(greet("World"))  # Output: Hello, World!
```

What kind of coding help do you need?""")
        elif "math" in user_content or "calculate" in user_content:
            return MockResponse("I can help with mathematical calculations! Try asking me to solve equations, explain concepts, or work through problems.")
        else:
            return MockResponse(f"I understand you said: '{user_content}'. I'm a demo version of Manus running without an API key. I can provide basic responses, but for full AI capabilities, please configure a real API key in config/config.toml.")
    
    async def run(self, prompt: str):
        """Run the agent with a given prompt."""
        try:
            self.current_step = 0
            
            # Add user message to conversation
            user_message = Message(role="user", content=prompt)
            self.conversation_history.append(user_message)
            
            # Convert to OpenAI format
            messages = [{"role": msg.role, "content": msg.content} for msg in self.conversation_history]
            
            # Add system message
            system_message = {
                "role": "system", 
                "content": "You are Manus, a helpful AI assistant running in demo mode."
            }
            messages.insert(0, system_message)
            
            logger.info("Processing request with Manus Demo...")
            
            # Get mock response
            response = await self.mock_llm_response(messages)
            
            if response and hasattr(response, 'content'):
                assistant_message = Message(role="assistant", content=response.content)
                self.conversation_history.append(assistant_message)
                
                logger.info("Response generated successfully")
                print(f"\n🤖 Manus: {response.content}")
                
            else:
                logger.warning("No response generated")
                print("\n🤖 Manus: I'm sorry, I couldn't process your request at the moment.")
                
        except Exception as e:
            logger.error(f"Error processing request: {e}")
            print(f"\n❌ Error: {e}")
    
    async def cleanup(self):
        """Cleanup resources."""
        logger.info("Cleaning up Manus Demo resources...")


async def main():
    """Main function - Demo version that works without API keys."""
    print("=" * 70)
    print("🚀 OpenManus Demo - No API Key Required")
    print("=" * 70)
    print(f"Python version: {sys.version}")
    print()
    
    # Check configuration
    try:
        api_key = config.llm['default'].api_key
        model = config.llm['default'].model
        
        print(f"📋 Configuration:")
        print(f"   Model: {model}")
        print(f"   API Key: {'✅ Set' if api_key and api_key not in ['demo-key', 'YOUR_API_KEY'] else '🔄 Demo Mode (no real API calls)'}")
        print()
        
        if api_key in ["demo-key", "YOUR_API_KEY"]:
            print("ℹ️  Running in DEMO MODE - using mock responses")
            print("   To enable real AI responses:")
            print("   1. Get an API key from https://platform.openai.com/api-keys")
            print("   2. Edit config/config.toml and replace 'demo-key' with your API key")
            print("   3. Run main_py39.py instead")
            print()
            
    except Exception as e:
        logger.error(f"Configuration error: {e}")
        print(f"❌ Configuration error: {e}")
        return
    
    # Create and initialize Manus agent
    agent = ManusDemo()
    
    try:
        prompt = input("Enter your prompt: ")
        if not prompt.strip():
            logger.warning("Empty prompt provided.")
            return

        logger.warning("Processing your request...")
        await agent.run(prompt)
        logger.info("Request processing completed.")
        
    except KeyboardInterrupt:
        logger.warning("Operation interrupted.")
        print("\n⚠️  Operation interrupted by user.")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        print(f"\n❌ Unexpected error: {e}")
    finally:
        # Ensure agent resources are cleaned up before exiting
        await agent.cleanup()
        print("\n👋 Goodbye!")


if __name__ == "__main__":
    asyncio.run(main())
