repos:
  - repo: https://github.com/psf/black
    rev: 23.1.0
    hooks:
      - id: black

  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files

  - repo: https://github.com/PyCQA/autoflake
    rev: v2.0.1
    hooks:
      - id: autoflake
        args: [
          --remove-all-unused-imports,
          --ignore-init-module-imports,
          --expand-star-imports,
          --remove-duplicate-keys,
          --remove-unused-variables,
          --recursive,
          --in-place,
          --exclude=__init__.py,
        ]
        files: \.py$

  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        args: [
          "--profile", "black",
          "--filter-files",
          "--lines-after-imports=2",
        ]
