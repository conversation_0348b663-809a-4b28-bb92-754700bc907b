#!/usr/bin/env python3
"""
Simplified version of main.py that avoids browser dependencies.
This version focuses on basic LLM functionality without browser automation.
"""

import async<PERSON>
from typing import List

from app.config import config
from app.llm import LLM
from app.logger import logger
from app.schema import Message


class SimpleManus:
    """A simplified version of Manus without browser dependencies."""
    
    def __init__(self):
        self.llm = LLM()
        self.conversation_history: List[Message] = []
    
    async def run(self, prompt: str):
        """Run the agent with a given prompt."""
        try:
            # Add user message to conversation
            user_message = Message(role="user", content=prompt)
            self.conversation_history.append(user_message)
            
            # Convert to OpenAI format
            messages = [{"role": msg.role, "content": msg.content} for msg in self.conversation_history]
            
            # Add system message
            system_message = {
                "role": "system", 
                "content": "You are a helpful AI assistant. You can help with various tasks but currently don't have access to browser automation or file system tools."
            }
            messages.insert(0, system_message)
            
            logger.info("Sending request to LLM...")
            
            # Get response from LLM
            response = await self.llm.ask(messages, timeout=60)
            
            if response and hasattr(response, 'content'):
                assistant_message = Message(role="assistant", content=response.content)
                self.conversation_history.append(assistant_message)
                
                logger.info("Response received:")
                print(f"\nAssistant: {response.content}")
            else:
                logger.warning("No response received from LLM")
                print("\nAssistant: I'm sorry, I couldn't process your request at the moment.")
                
        except Exception as e:
            logger.error(f"Error processing request: {e}")
            print(f"\nError: {e}")
            print("This might be due to an invalid API key or network issues.")
    
    async def cleanup(self):
        """Cleanup resources."""
        logger.info("Cleaning up resources...")


async def main():
    """Main function."""
    print("=" * 60)
    print("OpenManus - Simplified Version (Python 3.9 Compatible)")
    print("=" * 60)
    print("Note: This version runs without browser automation features.")
    print("To use the full version, please upgrade to Python 3.11+")
    print()
    
    # Check if we have a valid API key
    api_key = config.llm['default'].api_key
    if api_key == "demo-key" or api_key == "YOUR_API_KEY":
        print("⚠️  WARNING: Using demo/placeholder API key!")
        print("   To use the LLM functionality, please:")
        print("   1. Edit config/config.toml")
        print("   2. Add your actual OpenAI API key")
        print("   3. Restart the application")
        print()
    
    # Create and initialize simple agent
    agent = SimpleManus()
    
    try:
        while True:
            prompt = input("Enter your prompt (or 'quit' to exit): ")
            if not prompt.strip():
                logger.warning("Empty prompt provided.")
                continue
            
            if prompt.lower() in ['quit', 'exit', 'q']:
                break
            
            logger.info("Processing your request...")
            await agent.run(prompt)
            print()
            
    except KeyboardInterrupt:
        logger.warning("Operation interrupted.")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
    finally:
        # Ensure agent resources are cleaned up before exiting
        await agent.cleanup()
        print("Goodbye!")


if __name__ == "__main__":
    asyncio.run(main())
