import telebot
import random
import threading
import time
import json # لإضافة التعامل مع ملفات JSON
import os   # للتحقق من وجود الملفات وحذفها

# ✅ التوكن (Token)
# تأكد من عدم مشاركة توكن البوت الخاص بك أبداً
TOKEN = '8446491022:AAFz0Q1jf9sXxq_-Rp3leeSuzaVetDo8ulE'
bot = telebot.TeleBot(TOKEN)

# 🧠 تحميل الكلمات (Load Words)
# تأكد من وجود ملف words.txt في نفس مسار السكربت وأن الكلمات مكتوبة سطرًا واحدًا لكل كلمة.
try:
    with open("words.txt", "r", encoding="utf-8") as f:
        WORDS = [word.strip() for word in f.readlines() if word.strip()]
    if not WORDS:
        print("❗️ ملف الكلمات (words.txt) فارغ. يرجى إضافة كلمات إليه.")
        WORDS = ["بايرن", "ريال", "مدريد", "مانشستر", "برشلونة", "ميلان", "ليفربول", "يوفنتوس"] # كلمات احتياطية
except FileNotFoundError:
    print("❗️ ملف الكلمات (words.txt) غير موجود. يرجى إنشاء الملف وإضافة كلمات.")
    WORDS = ["بايرن", "ريال", "مدريد", "مانشستر", "برشلونة", "ميلان", "ليفربول", "يوفنتوس"] # كلمات احتياطية

# ⚙️ إعدادات اللعبة العامة (يمكن أن تكون ثابتة لجميع الجلسات أو يتم تخصيصها مستقبلاً)
POINTS_TO_WIN = 10  # عدد النقاط المطلوبة للفوز باللعبة
ROUND_TIMEOUT_SECONDS = 5  # المدة المتاحة للاعبين للإجابة على الكلمة
WINNER_PAUSE_SECONDS = 10  # مدة التوقف بعد فوز أحدهم بجولة قبل إرسال الكلمة التالية

# 👤 معرف المبرمج (Developer ID)
DEV_USERNAME = ""  # غيّر هذا المعرف بمعرف مطور البوت إذا أردت

# 📊 ملف قاعدة البيانات (JSON)
DB_FILE = 'game_data.json'

# 🔄 حالة اللعبة لجميع الجلسات (Game State for all sessions)
# هذا القاموس سيحتوي على حالة اللعبة لكل chat_id على حدة
# الهيكل: { chat_id: { 'points': {}, 'current_word': '', 'game_running': False, ... }, ... }
game_sessions = {}
# قاموس لتخزين مؤشرات الثريدات النشطة لكل كروب (لا يمكن حفظها في JSON)
running_game_threads = {}

# --- وظائف حفظ وتحميل بيانات الجلسات (JSON) ---
def load_game_sessions():
    """
    تحميل بيانات جميع جلسات اللعب من ملف JSON.
    إذا كانت هناك ألعاب نشطة في الملف، سيتم اعتبارها غير نشطة (false) عند التحميل لتجنب مشاكل الثريدات القديمة بعد إعادة تشغيل البوت.
    """
    global game_sessions
    if os.path.exists(DB_FILE):
        try:
            with open(DB_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)
                # التأكد من أن مفاتيح chat_id يتم تخزينها كـ string في JSON ثم تحويلها إلى int
                # وإعداد game_running = False لجميع الألعاب المحملة لمنع مشكلات "الثريدات الوهمية"
                game_sessions = {int(k): v for k, v in data.items()}
                for session_id, session_data in game_sessions.items():
                    if session_data.get('game_running', False):
                        session_data['game_running'] = False # إيقاف أي لعبة كانت قيد التشغيل عند إغلاق البوت
                        print(f"تم إيقاف لعبة في الكروب {session_id} بعد إعادة تشغيل البوت.")
            print(f"تم تحميل بيانات الجلسات من {DB_FILE}")
        except json.JSONDecodeError:
            print(f"❗️ خطأ في قراءة ملف JSON ({DB_FILE}). سيبدأ البوت بدون بيانات جلسات سابقة.")
            game_sessions = {}
        except Exception as e:
            print(f"❗️ حدث خطأ غير متوقع أثناء تحميل البيانات: {e}")
            game_sessions = {}
    else:
        print(f"الملف {DB_FILE} غير موجود. سيبدأ البوت بدون بيانات جلسات سابقة.")
        game_sessions = {}

def save_game_sessions():
    """
    حفظ بيانات جميع جلسات اللعب الحالية إلى ملف JSON.
    """
    try:
        with open(DB_FILE, 'w', encoding='utf-8') as f:
            # التأكد من أن مفاتيح chat_id هي strings قبل الحفظ في JSON
            serializable_sessions = {str(k): v for k, v in game_sessions.items()}
            json.dump(serializable_sessions, f, indent=4, ensure_ascii=False)
        # print(f"تم حفظ بيانات الجلسات في {DB_FILE}")
    except Exception as e:
        print(f"❗️ حدث خطأ أثناء حفظ البيانات إلى {DB_FILE}: {e}")

# تحميل الجلسات عند بدء تشغيل البوت
load_game_sessions()

# --- معالجات الأوامر والرسائل ---

@bot.message_handler(commands=["start"])
def send_welcome(message):
    """
    يرسل رسالة ترحيبية وتعليمات عند بدء البوت أو استخدام أمر /start.
    """
    markup = telebot.types.InlineKeyboardMarkup()
    markup.add(telebot.types.InlineKeyboardButton("👨‍💻 المطور", url=f"https://t.me/{DEV_USERNAME.lstrip('@')}"))
    
    # ➕ إضافة زر "أضف البوت إلى مجموعتك"
    try:
        bot_info = bot.get_me()
        add_to_group_url = f"https://t.me/{bot_info.username}?startgroup"
        markup.add(telebot.types.InlineKeyboardButton("➕ أضف البوت إلى مجموعتك", url=add_to_group_url))
    except telebot.apihelper.ApiTelegramException:
        print("فشل في الحصول على معلومات البوت لزر 'إضافة إلى مجموعة'. يرجى التأكد من التوكن.")
        
    bot.send_message(message.chat.id,
                     "👋 أهلاً بك في لعبة 'الأسرع'! 🚀\n"
                     "أنا بوت مسلي أتحدى أصدقائك في سرعة الكتابة.\n\n"
                     "لتبدأ اللعب، أضفني إلى مجموعة (جروب) لديك، ثم استخدم الأمر /startgame.\n"
                     "هدف اللعبة: كن أول من يكتب الكلمة الظاهرة لتحصل على نقطة! أول لاعب يصل إلى "
                     f"*{POINTS_TO_WIN}* نقاط يفوز! 🏆",
                     reply_markup=markup, parse_mode="Markdown")

@bot.message_handler(commands=["startgame"])
def start_game(message):
    """
    يبدأ اللعبة في الكروب، يقوم بتهيئة النقاط، ويرسل رسالة بداية.
    """
    chat_id = message.chat.id

    if message.chat.type == "private":
        bot.reply_to(message, "❗️ هذه اللعبة مصممة للعب في المجموعات (الكروبات). يرجى بدء اللعبة في كروب!")
        return

    # التحقق مما إذا كانت اللعبة قيد التشغيل بالفعل في هذا الكروب
    if game_sessions.get(chat_id, {}).get('game_running', False):
        bot.reply_to(message, "🔥 اللعبة شغالة بالفعل في هذه المجموعة! انتظر حتى تنتهي الجولة الحالية أو استخدم /stopgame لإنهائها.")
        return

    # تهيئة حالة اللعبة لهذا الكروب
    game_sessions[chat_id] = {
        'points': {},
        'current_word': "",
        'game_running': True,
        'pinned_message_id': None,
        'answered_this_round': False,
        'words_queue': random.sample(WORDS, len(WORDS)) # خلط الكلمات وتعبئة قائمة الانتظار
    }
    save_game_sessions() # حفظ حالة اللعبة الجديدة

    bot.send_message(chat_id, "🌟 بدء لعبة جديدة 'الأسرع'! 🌟\n"
                                     f"سأبدأ بإرسال الكلمات. أول من يكتب الكلمة صحيحة يحصل على نقطة.\n"
                                     f"اللاعب الذي يصل إلى *{POINTS_TO_WIN}* نقاط أولاً هو الفائز! 🏆\n\n"
                                     "حظاً سعيداً للجميع! 🍀", parse_mode="Markdown")

    # بدء حلقة اللعبة في مؤشر (Thread) منفصل لهذا الكروب
    game_thread = threading.Thread(target=game_loop, args=(chat_id,))
    running_game_threads[chat_id] = game_thread # تخزين مؤشر الثريد
    game_thread.start()

@bot.message_handler(commands=["stopgame"])
def stop_game(message):
    """
    يوقف اللعبة الحالية في الكروب المعني ويحذف بياناتها.
    """
    chat_id = message.chat.id

    if not game_sessions.get(chat_id, {}).get('game_running', False):
        bot.reply_to(message, "❗️ لا توجد لعبة نشطة في هذه المجموعة لإيقافها حالياً.")
        return

    # إرسال رسالة الإيقاف
    bot.send_message(chat_id, "✋ تم إيقاف اللعبة بواسطة المشرف. شكراً للمشاركة!")

    # تحديث حالة اللعبة في الذاكرة (وهذا سيجعل الثريد يتوقف بشكل طبيعي)
    session_data = game_sessions.get(chat_id)
    if session_data:
        session_data['game_running'] = False # إشارة للثريد للتوقف
        
        # محاولة إلغاء تثبيت الرسالة قبل حذف البيانات
        if session_data.get('pinned_message_id'):
            try:
                bot.unpin_chat_message(chat_id, session_data['pinned_message_id'])
            except telebot.apihelper.ApiTelegramException as e:
                print(f"فشل في إلغاء تثبيت الرسالة في الكروب {chat_id} عند الإيقاف: {e}")
            finally:
                session_data['pinned_message_id'] = None

    # حذف بيانات اللعبة من الذاكرة والملف
    if chat_id in game_sessions:
        del game_sessions[chat_id]
        save_game_sessions() # حفظ التغييرات في ملف JSON
        print(f"تم حذف بيانات اللعبة للكروب {chat_id}.")
    
    # محاولة انتظار انتهاء مؤشر اللعبة (إذا كان لا يزال يعمل)
    if chat_id in running_game_threads and running_game_threads[chat_id].is_alive():
        running_game_threads[chat_id].join(timeout=5) # الانتظار لمدة 5 ثوانٍ
        if running_game_threads[chat_id].is_alive():
            print(f"تحذير: مؤشر اللعبة للكروب {chat_id} لم ينته بشكل نظيف بعد أمر الإيقاف.")
        del running_game_threads[chat_id] # حذف مؤشر الثريد

def game_loop(chat_id):
    """
    الحلقة الرئيسية للعبة التي ترسل الكلمات وتدير توقيت الجولات لكروب معين.
    """
    # جلب بيانات الجلسة الخاصة بهذا الكروب
    session = game_sessions.get(chat_id)
    if not session: # إذا تم حذف الجلسة لأي سبب
        print(f"خطأ: جلسة اللعبة للكروب {chat_id} غير موجودة أثناء محاولة بدء game_loop.")
        return

    # إرسال أول رسالة لوحة المتصدرين وتثبيتها
    bot.send_message(chat_id, "🚀 اللعبة بدأت! جهزوا أصابعكم... ⚡️")
    leaderboard_text = generate_leaderboard(chat_id)
    try:
        sent_msg = bot.send_message(chat_id, leaderboard_text, parse_mode="Markdown")
        bot.pin_chat_message(chat_id, sent_msg.message_id, disable_notification=True)
        session['pinned_message_id'] = sent_msg.message_id
        save_game_sessions() # حفظ معرف الرسالة المثبتة
    except telebot.apihelper.ApiTelegramException as e:
        print(f"فشل في تثبيت الرسالة في الكروب {chat_id}: {e}")
        bot.send_message(chat_id, "⚠️ لم أستطع تثبيت رسالة المتصدرين. يرجى التأكد من صلاحياتي في الكروب.")
    except Exception as e:
        print(f"حدث خطأ غير متوقع أثناء تثبيت الرسالة في الكروب {chat_id}: {e}")

    while session.get('game_running', False): # التحقق من حالة اللعبة لهذا الكروب
        # إذا كانت اللعبة قد انتهت بواسطة handle_answer (لوصول لاعب لـ 10 نقاط) أو بواسطة /stopgame
        if not session['game_running']:
            break

        # اختيار كلمة جديدة من قائمة الانتظار
        if not session.get('words_queue'):
            session['words_queue'] = random.sample(WORDS, len(WORDS)) # إعادة خلط وتعبئة الكلمات إذا نفدت
        session['current_word'] = session['words_queue'].pop(0) # الحصول على الكلمة الأولى من القائمة

        session['answered_this_round'] = False  # إعادة تعيين علامة الإجابة لكل جولة جديدة
        
        # إرسال الكلمة الجديدة
        bot.send_message(chat_id, f"✍️ أسرع واحد يكتب: `{session['current_word']}`")

        # إعطاء اللاعبين وقتاً للإجابة (ROUND_TIMEOUT_SECONDS)
        start_time = time.time()
        while time.time() - start_time < ROUND_TIMEOUT_SECONDS and not session['answered_this_round'] and session['game_running']:
            time.sleep(0.1)

        # التحقق مرة أخرى إذا كانت اللعبة لا تزال تعمل
        if not session['game_running']:
            break

        # إذا تمت الإجابة على الكلمة بنجاح
        if session['answered_this_round'] and session['game_running']: 
            bot.send_message(chat_id, f"⏱️ استراحة قصيرة... الكلمة التالية ستصل بعد {WINNER_PAUSE_SECONDS} ثوانٍ! ☕️")
            time.sleep(WINNER_PAUSE_SECONDS)
        elif not session['answered_this_round'] and session['game_running']:
            # إذا لم يقم أحد بالإجابة في الوقت المحدد
            bot.send_message(chat_id, f"😔 فاتكم الدور! الكلمة الصحيحة كانت: `{session['current_word']}`. جربوا مرة أخرى! 🚀")
            time.sleep(3) 
        
        save_game_sessions() # حفظ حالة الجلسة بعد كل جولة (خاصة بعد تغيير الكلمة والتحقق من game_running)

    # تنظيف الثريد عند نهاية اللعبة (سواء بالفوز أو بالإيقاف)
    if session.get('pinned_message_id'):
        try:
            bot.unpin_chat_message(chat_id, session['pinned_message_id'])
        except Exception as e:
            print(f"فشل في إلغاء تثبيت الرسالة في نهاية اللعبة لكروب {chat_id} (game_loop final cleanup): {e}")
        finally:
            session['pinned_message_id'] = None
    
    # بعد أن يخرج الثريد من الحلقة، يتم التأكد من أن بيانات الجلسة تم حذفها
    # هذا يتم بالفعل بواسطة stop_game أو handle_answer عندما يفوز أحدهم.
    # هنا للتأكد فقط من حالة الثريد في running_game_threads
    if chat_id in running_game_threads:
        del running_game_threads[chat_id]


def generate_leaderboard(chat_id, winner=None):
    """
    ينشئ نص لوحة المتصدرين بناءً على النقاط الحالية للاعبين لكروب معين.
    """
    session = game_sessions.get(chat_id)
    if not session or not session.get('points'):
        return "🏆 *لوحة المتصدرين* 🏆\nلا يوجد نقاط بعد! ابدأوا اللعب! 🏃‍♂️💨"

    points = session['points']
    sorted_players = sorted(points.items(), key=lambda x: x[1], reverse=True)
    leaderboard_text = "🏆 *لوحة المتصدرين* 🏆\n"
    for idx, (name, score) in enumerate(sorted_players):
        if idx == 0:
            leaderboard_text += f"🥇 {name} — {score} نقطة\n"
        elif idx == 1:
            leaderboard_text += f"🥈 {name} — {score} نقطة\n"
        elif idx == 2:
            leaderboard_text += f"🥉 {name} — {score} نقطة\n"
        else:
            leaderboard_text += f"▪️ {name} — {score} نقطة\n"
    
    if winner:
        leaderboard_text += f"\n✨ الفائز الأخير: *{winner}*! تهانينا! 🎉"
    
    leaderboard_text += f"\n\nالهدف: {POINTS_TO_WIN} نقطة! 🚀"
    return leaderboard_text

def update_leaderboard(chat_id, winner=None):
    """
    يحدث رسالة لوحة المتصدرين المثبتة في الكروب.
    """
    session = game_sessions.get(chat_id)
    if not session or not session.get('pinned_message_id') or not session.get('game_running', False):
        return # لا توجد جلسة أو رسالة مثبتة أو اللعبة غير نشطة

    leaderboard_text = generate_leaderboard(chat_id, winner)
    try:
        bot.edit_message_text(leaderboard_text, chat_id, session['pinned_message_id'], parse_mode="Markdown")
        save_game_sessions() # حفظ تحديثات لوحة المتصدرين
    except telebot.apihelper.ApiTelegramException as e:
        if "message is not modified" in str(e):
            pass
        elif "message to edit not found" in str(e) or "message can't be edited" in str(e):
            print(f"رسالة لوحة المتصدرين للكروب {chat_id} لم تُعثر عليها أو لا يمكن تعديلها. الخطأ: {e}")
            session['pinned_message_id'] = None # مسح المعرف لتجنب محاولة التعديل مرة أخرى
        else:
            print(f"خطأ بتحديث لوحة المتصدرين للكروب {chat_id}: {e}")
    except Exception as e:
        print(f"خطأ غير متوقع بتحديث لوحة المتصدرين للكروب {chat_id}: {e}")


# 🚫 معالج جديد للرسالة "الكلمة لاتحتسب"
@bot.message_handler(func=lambda msg: game_sessions.get(msg.chat.id, {}).get('game_running', False) and msg.text.strip() == "الكلمة لاتحتسب")
def handle_word_not_counted(message):
    """
    يتعامل مع الرسالة المحددة "الكلمة لاتحتسب" ولا يحتسبها كنقطة.
    """
    bot.send_message(message.chat.id, "🚫 هذه الكلمة لا تحتسب كنقطة في اللعبة! الرجاء كتابة الكلمة الصحيحة المعروضة.")


@bot.message_handler(func=lambda msg: game_sessions.get(msg.chat.id, {}).get('game_running', False) and not game_sessions.get(msg.chat.id, {}).get('answered_this_round', True))
def handle_answer(message):
    """
    يتعامل مع إجابات اللاعبين. يتحقق من صحة الإجابة، يمنح النقاط، ويحتفل بالفائز بالجولة.
    """
    chat_id = message.chat.id
    session = game_sessions.get(chat_id)

    # فحص إضافي للتأكد من أن اللعبة نشطة ولم يتم الإجابة على الكلمة بعد
    if not session or not session.get('game_running', False) or session.get('answered_this_round', True):
        return
    
    # تأكد أن الرسالة ليست "الكلمة لاتحتسب" حتى لو تخطت المعالج السابق (تأمين إضافي)
    if message.text.strip() == "الكلمة لاتحتسب":
        return

    # مقارنة الإجابة (تجاهل المسافات الزائدة وتحويلها إلى أحرف صغيرة)
    if message.text.strip().lower() == session['current_word'].lower():
        session['answered_this_round'] = True  # وضع علامة بأن الكلمة قد تمت الإجابة عليها في هذه الجولة

        full_name = message.from_user.first_name
        if message.from_user.last_name:
            full_name += f" {message.from_user.last_name}"

        # تحديث نقاط اللاعب
        session['points'][full_name] = session['points'].get(full_name, 0) + 1
        save_game_sessions() # حفظ النقاط المحدثة

        # التحقق ما إذا كان هذا اللاعب قد فاز باللعبة ككل
        if session['points'][full_name] >= POINTS_TO_WIN:
            session['game_running'] = False # إيقاف اللعبة على الفور

            # إرسال رسالة الفوز النهائية
            final_leaderboard = generate_leaderboard(chat_id, winner=full_name)
            bot.send_message(chat_id, 
                             f"🎉 مبروك *{full_name}*، لقد فزت باللعبة! 🥳 وصلت إلى {POINTS_TO_WIN} نقطة!\n\n{final_leaderboard}", 
                             parse_mode="Markdown")
            
            # إلغاء تثبيت لوحة المتصدرين
            if session.get('pinned_message_id'):
                try:
                    bot.unpin_chat_message(chat_id, session['pinned_message_id'])
                except Exception as e:
                    print(f"فشل في إلغاء تثبيت الرسالة عند انتهاء اللعبة للكروب {chat_id} بواسطة handle_answer: {e}")
                finally:
                    session['pinned_message_id'] = None
            
            # حذف بيانات اللعبة من الذاكرة والملف بعد انتهاء اللعبة بالفوز
            if chat_id in game_sessions:
                del game_sessions[chat_id]
                save_game_sessions() # حفظ التغييرات في ملف JSON
                print(f"تم حذف بيانات اللعبة للكروب {chat_id} بعد فوز اللاعب.")
            
            # محاولة انتظار انتهاء مؤشر اللعبة (إذا كان لا يزال يعمل)
            if chat_id in running_game_threads and running_game_threads[chat_id].is_alive():
                running_game_threads[chat_id].join(timeout=5)
                if running_game_threads[chat_id].is_alive():
                    print(f"تحذير: مؤشر اللعبة للكروب {chat_id} لم ينته بشكل نظيف بعد الفوز.")
                del running_game_threads[chat_id]

            return # الخروج من الدالة بعد انتهاء اللعبة

        # إذا لم تنته اللعبة بعد، إرسال رسالة الفوز بالجولة وتحديث لوحة المتصدرين
        bot.send_message(chat_id, f"🎉 نقطة! مبروك أنت الأسرع يا *{full_name}*! 🥳\nنقاطك الحالية: {session['points'][full_name]} 🎯", parse_mode="Markdown")
        update_leaderboard(chat_id)
    else:
        pass

print("🤖 البوت يعمل... جاهز للانطلاق! 🚀")
# تشغيل البوت بشكل مستمر
# تأكد أن هذا الجزء في نهاية السكربت.
bot.infinity_polling()