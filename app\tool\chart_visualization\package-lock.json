{"name": "chart_visualization", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "chart_visualization", "version": "1.0.0", "license": "ISC", "dependencies": {"@visactor/vchart": "^1.13.7", "@visactor/vmind": "2.0.5", "canvas": "^2.11.2", "get-stdin": "^9.0.0"}, "devDependencies": {"@types/node": "^22.10.1", "ts-node": "^10.9.2", "typescript": "^5.7.2"}}, "node_modules/@cspotcode/source-map-support": {"version": "0.8.1", "resolved": "https://registry.npmjs.org/@cspotcode/source-map-support/-/source-map-support-0.8.1.tgz", "integrity": "sha512-IchNf6dN4tHoMFIn/7OE8LWZ19Y6q/67Bmf6vnGREv8RSbBVb9LPJxEcnwrcwX6ixSvaiGoomAUvu4YSxXrVgw==", "dev": true, "dependencies": {"@jridgewell/trace-mapping": "0.3.9"}, "engines": {"node": ">=12"}}, "node_modules/@jridgewell/resolve-uri": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz", "integrity": "sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==", "dev": true, "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/sourcemap-codec": {"version": "1.5.0", "resolved": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz", "integrity": "sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==", "dev": true}, "node_modules/@jridgewell/trace-mapping": {"version": "0.3.9", "resolved": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.9.tgz", "integrity": "sha512-3Belt6tdc8bPgAtbcmdtNJlirVoTmEb5e2gC94PnkwEW9jI6CAHUeoG85tjWP5WquqfavoMtMwiG4P926ZKKuQ==", "dev": true, "dependencies": {"@jridgewell/resolve-uri": "^3.0.3", "@jridgewell/sourcemap-codec": "^1.4.10"}}, "node_modules/@mapbox/node-pre-gyp": {"version": "1.0.11", "resolved": "https://registry.npmjs.org/@mapbox/node-pre-gyp/-/node-pre-gyp-1.0.11.tgz", "integrity": "sha512-Yhlar6v9WQgUp/He7BdgzOz8lqMQ8sU+jkCq7Wx8Myc5YFJLbEe7lgui/V7G1qB1DJykHSGwreceSaD60Y0PUQ==", "dependencies": {"detect-libc": "^2.0.0", "https-proxy-agent": "^5.0.0", "make-dir": "^3.1.0", "node-fetch": "^2.6.7", "nopt": "^5.0.0", "npmlog": "^5.0.1", "rimraf": "^3.0.2", "semver": "^7.3.5", "tar": "^6.1.11"}, "bin": {"node-pre-gyp": "bin/node-pre-gyp"}}, "node_modules/@resvg/resvg-js": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/@resvg/resvg-js/-/resvg-js-2.4.1.tgz", "integrity": "sha512-wTOf1zerZX8qYcMmLZw3czR4paI4hXqPjShNwJRh5DeHxvgffUS5KM7XwxtbIheUW6LVYT5fhT2AJiP6mU7U4A==", "engines": {"node": ">= 10"}, "optionalDependencies": {"@resvg/resvg-js-android-arm-eabi": "2.4.1", "@resvg/resvg-js-android-arm64": "2.4.1", "@resvg/resvg-js-darwin-arm64": "2.4.1", "@resvg/resvg-js-darwin-x64": "2.4.1", "@resvg/resvg-js-linux-arm-gnueabihf": "2.4.1", "@resvg/resvg-js-linux-arm64-gnu": "2.4.1", "@resvg/resvg-js-linux-arm64-musl": "2.4.1", "@resvg/resvg-js-linux-x64-gnu": "2.4.1", "@resvg/resvg-js-linux-x64-musl": "2.4.1", "@resvg/resvg-js-win32-arm64-msvc": "2.4.1", "@resvg/resvg-js-win32-ia32-msvc": "2.4.1", "@resvg/resvg-js-win32-x64-msvc": "2.4.1"}}, "node_modules/@resvg/resvg-js-android-arm-eabi": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/@resvg/resvg-js-android-arm-eabi/-/resvg-js-android-arm-eabi-2.4.1.tgz", "integrity": "sha512-AA6f7hS0FAPpvQMhBCf6f1oD1LdlqNXKCxAAPpKh6tR11kqV0YIB9zOlIYgITM14mq2YooLFl6XIbbvmY+jwUw==", "cpu": ["arm"], "optional": true, "os": ["android"], "engines": {"node": ">= 10"}}, "node_modules/@resvg/resvg-js-android-arm64": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/@resvg/resvg-js-android-arm64/-/resvg-js-android-arm64-2.4.1.tgz", "integrity": "sha512-/QleoRdPfsEuH9jUjilYcDtKK/BkmWcK+1LXM8L2nsnf/CI8EnFyv7ZzCj4xAIvZGAy9dTYr/5NZBcTwxG2HQg==", "cpu": ["arm64"], "optional": true, "os": ["android"], "engines": {"node": ">= 10"}}, "node_modules/@resvg/resvg-js-darwin-arm64": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/@resvg/resvg-js-darwin-arm64/-/resvg-js-darwin-arm64-2.4.1.tgz", "integrity": "sha512-U1oMNhea+kAXgiEXgzo7EbFGCD1Edq5aSlQoe6LMly6UjHzgx2W3N5kEXCwU/CgN5FiQhZr7PlSJSlcr7mdhfg==", "cpu": ["arm64"], "optional": true, "os": ["darwin"], "engines": {"node": ">= 10"}}, "node_modules/@resvg/resvg-js-darwin-x64": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/@resvg/resvg-js-darwin-x64/-/resvg-js-darwin-x64-2.4.1.tgz", "integrity": "sha512-avyVh6DpebBfHHtTQTZYSr6NG1Ur6TEilk1+H0n7V+g4F7x7WPOo8zL00ZhQCeRQ5H4f8WXNWIEKL8fwqcOkYw==", "cpu": ["x64"], "optional": true, "os": ["darwin"], "engines": {"node": ">= 10"}}, "node_modules/@resvg/resvg-js-linux-arm-gnueabihf": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/@resvg/resvg-js-linux-arm-gnueabihf/-/resvg-js-linux-arm-gnueabihf-2.4.1.tgz", "integrity": "sha512-isY/mdKoBWH4VB5v621co+8l101jxxYjuTkwOLsbW+5RK9EbLciPlCB02M99ThAHzI2MYxIUjXNmNgOW8btXvw==", "cpu": ["arm"], "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}}, "node_modules/@resvg/resvg-js-linux-arm64-gnu": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/@resvg/resvg-js-linux-arm64-gnu/-/resvg-js-linux-arm64-gnu-2.4.1.tgz", "integrity": "sha512-uY5voSCrFI8TH95vIYBm5blpkOtltLxLRODyhKJhGfskOI7XkRw5/t1u0sWAGYD8rRSNX+CA+np86otKjubrNg==", "cpu": ["arm64"], "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}}, "node_modules/@resvg/resvg-js-linux-arm64-musl": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/@resvg/resvg-js-linux-arm64-musl/-/resvg-js-linux-arm64-musl-2.4.1.tgz", "integrity": "sha512-6mT0+JBCsermKMdi/O2mMk3m7SqOjwi9TKAwSngRZ/nQoL3Z0Z5zV+572ztgbWr0GODB422uD8e9R9zzz38dRQ==", "cpu": ["arm64"], "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}}, "node_modules/@resvg/resvg-js-linux-x64-gnu": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/@resvg/resvg-js-linux-x64-gnu/-/resvg-js-linux-x64-gnu-2.4.1.tgz", "integrity": "sha512-60KnrscLj6VGhkYOJEmmzPlqqfcw1keDh6U+vMcNDjPhV3B5vRSkpP/D/a8sfokyeh4VEacPSYkWGezvzS2/mg==", "cpu": ["x64"], "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}}, "node_modules/@resvg/resvg-js-linux-x64-musl": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/@resvg/resvg-js-linux-x64-musl/-/resvg-js-linux-x64-musl-2.4.1.tgz", "integrity": "sha512-0AMyZSICC1D7ge115cOZQW8Pcad6PjWuZkBFF3FJuSxC6Dgok0MQnLTs2MfMdKBlAcwO9dXsf3bv9tJZj8pATA==", "cpu": ["x64"], "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}}, "node_modules/@resvg/resvg-js-win32-arm64-msvc": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/@resvg/resvg-js-win32-arm64-msvc/-/resvg-js-win32-arm64-msvc-2.4.1.tgz", "integrity": "sha512-76XDFOFSa3d0QotmcNyChh2xHwk+JTFiEQBVxMlHpHMeq7hNrQJ1IpE1zcHSQvrckvkdfLboKRrlGB86B10Qjw==", "cpu": ["arm64"], "optional": true, "os": ["win32"], "engines": {"node": ">= 10"}}, "node_modules/@resvg/resvg-js-win32-ia32-msvc": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/@resvg/resvg-js-win32-ia32-msvc/-/resvg-js-win32-ia32-msvc-2.4.1.tgz", "integrity": "sha512-odyVFGrEWZIzzJ89KdaFtiYWaIJh9hJRW/frcEcG3agJ464VXkN/2oEVF5ulD+5mpGlug9qJg7htzHcKxDN8sg==", "cpu": ["ia32"], "optional": true, "os": ["win32"], "engines": {"node": ">= 10"}}, "node_modules/@resvg/resvg-js-win32-x64-msvc": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/@resvg/resvg-js-win32-x64-msvc/-/resvg-js-win32-x64-msvc-2.4.1.tgz", "integrity": "sha512-vY4kTLH2S3bP+puU5x7hlAxHv+ulFgcK6Zn3efKSr0M0KnZ9A3qeAjZteIpkowEFfUeMPNg2dvvoFRJA9zqxSw==", "cpu": ["x64"], "optional": true, "os": ["win32"], "engines": {"node": ">= 10"}}, "node_modules/@stdlib/array-base-filled": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/array-base-filled/-/array-base-filled-0.2.2.tgz", "integrity": "sha512-T7nB7dni5Y4/nsq6Gc1bAhYfzJbcOdqsmVZJUI698xpDbhCdVCIIaEbf0PnDMGN24psN+5mgAVmnNBom+uF0Xg==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/array-base-zeros": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/array-base-zeros/-/array-base-zeros-0.2.2.tgz", "integrity": "sha512-iwxqaEtpi4c2qpqabmhFdaQGkzgo5COwjHPn2T0S0wfJuM1VuVl5UBl15syr+MmZPJQOB1eBbh6F1uTh9597qw==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/array-base-filled": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/array-float32": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/array-float32/-/array-float32-0.2.2.tgz", "integrity": "sha512-pTcy1FNQrrJLL1LMxJjuVpcKJaibbGCFFTe41iCSXpSOC8SuTBuNohrO6K9+xR301Ruxxn4yrzjJJ6Fa3nQJ2g==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/assert-has-float32array-support": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/array-float64": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/array-float64/-/array-float64-0.2.2.tgz", "integrity": "sha512-ZmV5wcacGrhT0maw9dfLXNv4N3ZwFUV3D7ItFfZFGFnKIJbubrWzwtaYnxzIXigrDc8g3F6FVHRpsQLMxq0/lA==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/assert-has-float64array-support": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/array-uint16": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/array-uint16/-/array-uint16-0.2.2.tgz", "integrity": "sha512-z5c/Izw43HkKfb1pTgEUMAS8GFvhtHkkHZSjX3XJN+17P0VjknxjlSvPiCBGqaDX9jXtlWH3mn1LSyDKtJQoeA==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/assert-has-uint16array-support": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/array-uint32": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/array-uint32/-/array-uint32-0.2.2.tgz", "integrity": "sha512-3T894I9C2MqZJJmRCYFTuJp4Qw9RAt+GzYnVPyIXoK1h3TepUXe9VIVx50cUFIibdXycgu0IFGASeAb3YMyupw==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/assert-has-uint32array-support": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/array-uint8": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/array-uint8/-/array-uint8-0.2.2.tgz", "integrity": "sha512-Ip9MUC8+10U9x0crMKWkpvfoUBBhWzc6k5SI4lxx38neFVmiJ3f+5MBADEagjpoKSBs71vlY2drnEZe+Gs2Ytg==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/assert-has-uint8array-support": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/assert-has-float32array-support": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/assert-has-float32array-support/-/assert-has-float32array-support-0.2.2.tgz", "integrity": "sha512-pi2akQl8mVki43fF1GNQVLYW0bHIPp2HuRNThX9GjB3OFQTpvrV8/3zPSh4lOxQa5gRiabgf0+Rgeu3AOhEw9A==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/assert-is-float32array": "^0.2.2", "@stdlib/constants-float64-pinf": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/assert-has-float64array-support": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/assert-has-float64array-support/-/assert-has-float64array-support-0.2.2.tgz", "integrity": "sha512-8L3GuKY1o0dJARCOsW9MXcugXapaMTpSG6dGxyNuUVEvFfY5UOzcj9/JIDal5FjqSgqVOGL5qZl2qtRwub34VA==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/assert-is-float64array": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/assert-has-generator-support": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/assert-has-generator-support/-/assert-has-generator-support-0.2.2.tgz", "integrity": "sha512-TcE9BGV8i7B2OmxPlJ/2DUrAwG0W4fFS/DE7HmVk68PXVZsgyNQ/WP/IHBoazHDjhN5c3dU21c20kM/Bw007Rw==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/utils-eval": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/assert-has-own-property": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/assert-has-own-property/-/assert-has-own-property-0.2.2.tgz", "integrity": "sha512-m5rV4Z2/iNkwx2vRsNheM6sQZMzc8rQQOo90LieICXovXZy8wA5jNld4kRKjMNcRt/TjrNP7i2Rhh8hruRDlHg==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/assert-has-symbol-support": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/assert-has-symbol-support/-/assert-has-symbol-support-0.2.2.tgz", "integrity": "sha512-vCsGGmDZz5dikGgdF26rIL0y0nHvH7qaVf89HLLTybceuZijAqFSJEqcB3Gpl5uaeueLNAWExHi2EkoUVqKHGg==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/assert-has-tostringtag-support": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/assert-has-tostringtag-support/-/assert-has-tostringtag-support-0.2.2.tgz", "integrity": "sha512-bSHGqku11VH0swPEzO4Y2Dr+lTYEtjSWjamwqCTC8udOiOIOHKoxuU4uaMGKJjVfXG1L+XefLHqzuO5azxdRaA==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/assert-has-symbol-support": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/assert-has-uint16array-support": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/assert-has-uint16array-support/-/assert-has-uint16array-support-0.2.2.tgz", "integrity": "sha512-aL188V7rOkkEH4wYjfpB+1waDO4ULxo5ppGEK6X0kG4YiXYBL2Zyum53bjEQvo0Nkn6ixe18dNzqqWWytBmDeg==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/assert-is-uint16array": "^0.2.1", "@stdlib/constants-uint16-max": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/assert-has-uint32array-support": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/assert-has-uint32array-support/-/assert-has-uint32array-support-0.2.2.tgz", "integrity": "sha512-+UHKP3mZOACkJ9CQjeKNfbXHm5HGQB862V5nV5q3UQlHPzhslnXKyG1SwAxTx+0g88C/2vlDLeqG8H4TH2UTFA==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/assert-is-uint32array": "^0.2.1", "@stdlib/constants-uint32-max": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/assert-has-uint8array-support": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/assert-has-uint8array-support/-/assert-has-uint8array-support-0.2.2.tgz", "integrity": "sha512-VfzrB0BMik9MvPyKcMDJL3waq4nM30RZUrr2EuuQ/RbUpromRWSDbzGTlRq5SfjtJrHDxILPV3rytDCc03dgWA==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/assert-is-uint8array": "^0.2.1", "@stdlib/constants-uint8-max": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/assert-is-array": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/assert-is-array/-/assert-is-array-0.2.2.tgz", "integrity": "sha512-aJyTX2U3JqAGCATgaAX9ygvDHc97GCIKkIhiZm/AZaLoFHPtMA1atQ4bKcefEC8Um9eefryxTHfFPfSr9CoNQQ==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/utils-native-class": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/assert-is-big-endian": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/assert-is-big-endian/-/assert-is-big-endian-0.2.2.tgz", "integrity": "sha512-mPEl30/bqZh++UyQbxlyOuB7k0wC73y5J9nD2J6Ud6Fcl76R5IAGHRW0WT3W18is/6jG1jzMd8hrISFyD7N0sA==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/array-uint16": "^0.2.1", "@stdlib/array-uint8": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/assert-is-boolean": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/assert-is-boolean/-/assert-is-boolean-0.2.2.tgz", "integrity": "sha512-3KFLRTYZpX6u95baZ6PubBvjehJs2xBU6+zrenR0jx8KToUYCnJPxqqj7JXRhSD+cOURmcjj9rocVaG9Nz18Pg==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/assert-has-tostringtag-support": "^0.2.2", "@stdlib/boolean-ctor": "^0.2.2", "@stdlib/utils-define-nonenumerable-read-only-property": "^0.2.2", "@stdlib/utils-native-class": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/assert-is-buffer": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/assert-is-buffer/-/assert-is-buffer-0.2.2.tgz", "integrity": "sha512-4/WMFTEcDYlVbRhxY8Wlqag4S70QCnn6WmQ4wmfiLW92kqQHsLvTNvdt/qqh/SDyDV31R/cpd3QPsVN534dNEA==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/assert-is-object-like": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/assert-is-float32array": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/assert-is-float32array/-/assert-is-float32array-0.2.2.tgz", "integrity": "sha512-hxEKz/Y4m1NYuOaiQKoqQA1HeAYwNXFqSk3FJ4hC71DuGNit2tuxucVyck3mcWLpLmqo0+Qlojgwo5P9/C/9MQ==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/utils-native-class": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/assert-is-float64array": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/assert-is-float64array/-/assert-is-float64array-0.2.2.tgz", "integrity": "sha512-3R1wLi6u/IHXsXMtaLnvN9BSpqAJ8tWhwjOOr6kadDqCWsU7Odc7xKLeAXAInAxwnV8VDpO4ifym4A3wehazPQ==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/utils-native-class": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/assert-is-function": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/assert-is-function/-/assert-is-function-0.2.2.tgz", "integrity": "sha512-whY69DUYWljCJ79Cvygp7VzWGOtGTsh3SQhzNuGt+ut6EsOW+8nwiRkyBXYKf/MOF+NRn15pxg8cJEoeRgsPcA==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/utils-type-of": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/assert-is-little-endian": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/assert-is-little-endian/-/assert-is-little-endian-0.2.2.tgz", "integrity": "sha512-KMzPndj85jDiE1+hYCpw12k2OQOVkfpCo7ojCmCl8366wtKGEaEdGbz1iH98zkxRvnZLSMXcYXI2z3gtdmB0Ag==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/array-uint16": "^0.2.1", "@stdlib/array-uint8": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/assert-is-number": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/assert-is-number/-/assert-is-number-0.2.2.tgz", "integrity": "sha512-sWpJ59GqGbmlcdYSUV/OYkmQW8k47w10+E0K0zPu1x1VKzhjgA5ZB2sJcpgI8Vt3ckRLjdhuc62ZHJkrJujG7A==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/assert-has-tostringtag-support": "^0.2.2", "@stdlib/number-ctor": "^0.2.2", "@stdlib/utils-define-nonenumerable-read-only-property": "^0.2.2", "@stdlib/utils-native-class": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/assert-is-object": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/assert-is-object/-/assert-is-object-0.2.2.tgz", "integrity": "sha512-sNnphJuHyMDHHHaonlx6vaCKMe4sHOn0ag5Ck4iW3kJtM2OZB2J4h8qFcwKzlMk7fgFu7vYNGCZtpm1dYbbUfQ==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/assert-is-array": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/assert-is-object-like": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/assert-is-object-like/-/assert-is-object-like-0.2.2.tgz", "integrity": "sha512-MjQBpHdEebbJwLlxh/BKNH8IEHqY0YlcCMRKOQU0UOlILSJg0vG+GL4fDDqtx9FSXxcTqC+w3keHx8kAKvQhzg==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/assert-tools-array-function": "^0.2.1", "@stdlib/utils-define-nonenumerable-read-only-property": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/assert-is-plain-object": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/assert-is-plain-object/-/assert-is-plain-object-0.2.2.tgz", "integrity": "sha512-o4AFWgBsSNzZAOOfIrxoDFYTqnLuGiaHDFwIeZGUHdpQeav2Fll+sGeaqOcekF7yKawoswnwWdJqTsjapb4Yzw==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/assert-has-own-property": "^0.2.1", "@stdlib/assert-is-function": "^0.2.1", "@stdlib/assert-is-object": "^0.2.1", "@stdlib/utils-get-prototype-of": "^0.2.1", "@stdlib/utils-native-class": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/assert-is-regexp": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/assert-is-regexp/-/assert-is-regexp-0.2.2.tgz", "integrity": "sha512-2JtiUtRJxPaVXL7dkWoV3n5jouI65DwYDXsDXg3xo23TXlTNGgU/HhKO4FWC1Yqju7YMZi0hcZSW6E9v8ISqeQ==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/assert-has-tostringtag-support": "^0.2.2", "@stdlib/utils-native-class": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/assert-is-string": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/assert-is-string/-/assert-is-string-0.2.2.tgz", "integrity": "sha512-SOkFg4Hq443hkadM4tzcwTHWvTyKP9ULOZ8MSnnqmU0nBX1zLVFLFGY8jnF6Cary0dL0V7QQBCfuxqKFM6u2PQ==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/assert-has-tostringtag-support": "^0.2.2", "@stdlib/utils-define-nonenumerable-read-only-property": "^0.2.2", "@stdlib/utils-native-class": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/assert-is-uint16array": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/assert-is-uint16array/-/assert-is-uint16array-0.2.2.tgz", "integrity": "sha512-w3+HeTiXGLJGw5nCqr0WbvgArNMEj7ulED1Yd19xXbmmk2W1ZUB+g9hJDOQTiKsTU4AVyH4/As+aA8eDVmWtmg==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/utils-native-class": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/assert-is-uint32array": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/assert-is-uint32array/-/assert-is-uint32array-0.2.2.tgz", "integrity": "sha512-3F4nIHg1Qp0mMIsImWUC8DwQ3qBK5vdIJTjS2LufLbFBhHNmv5kK1yJiIXQDTLkENU0STZe05TByo01ZNLOmDQ==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/utils-native-class": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/assert-is-uint8array": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/assert-is-uint8array/-/assert-is-uint8array-0.2.2.tgz", "integrity": "sha512-51WnDip6H2RrN0CbqWmfqySAjam8IZ0VjlfUDc3PtcgrZGrKKjVgyHAsT/L3ZDydwF+aB94uvYJu5QyrCPNaZw==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/utils-native-class": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/assert-tools-array-function": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/assert-tools-array-function/-/assert-tools-array-function-0.2.2.tgz", "integrity": "sha512-FYeT7X9x0C8Nh+MN6IJUDz+7i7yB6mio2/SDlrvyepjyPSU/cfHfwW0GEOnQhxZ+keLZC/YqDD930WjRODwMdA==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/assert-is-array": "^0.2.1", "@stdlib/error-tools-fmtprodmsg": "^0.2.2", "@stdlib/string-format": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/boolean-ctor": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/boolean-ctor/-/boolean-ctor-0.2.2.tgz", "integrity": "sha512-qIkHzmfxDvGzQ3XI9R7sZG97QSaWG5TvWVlrvcysOGT1cs6HtQgnf4D//SRzZ52VLm8oICP+6OKtd8Hpm6G7Ww==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/complex-float32": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/@stdlib/complex-float32/-/complex-float32-0.2.1.tgz", "integrity": "sha512-tp83HfJzcZLK7/6P6gZPcAa/8F/aHS7gBHgB6ft45d/n6oE+/VbnyOvsJKanRv8S96kBRj8xkvlWHz4IiBrT0Q==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/assert-is-number": "^0.2.1", "@stdlib/number-float64-base-to-float32": "^0.2.1", "@stdlib/string-format": "^0.2.1", "@stdlib/utils-define-nonenumerable-read-only-property": "^0.2.1", "@stdlib/utils-define-property": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/complex-float32-ctor": {"version": "0.0.2", "resolved": "https://registry.npmjs.org/@stdlib/complex-float32-ctor/-/complex-float32-ctor-0.0.2.tgz", "integrity": "sha512-QsTLynhTRmDT0mSkfdHj0FSqQSxh2nKx+vvrH3Y0/Cd/r0WoHFZwyibndDxshfkf9B7nist8QKyvV82I3IZciA==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/assert-is-number": "^0.2.2", "@stdlib/error-tools-fmtprodmsg": "^0.2.2", "@stdlib/number-float64-base-to-float32": "^0.2.1", "@stdlib/string-format": "^0.2.2", "@stdlib/utils-define-nonenumerable-read-only-property": "^0.2.2", "@stdlib/utils-define-property": "^0.2.4"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/complex-float32-reim": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/@stdlib/complex-float32-reim/-/complex-float32-reim-0.1.2.tgz", "integrity": "sha512-24H+t1xwQF6vhOoMZdDA3TFB4M+jb5Swm/FwNaepovlzVIG2NlthUZs6mZg1T3oegqesIRQRwhpn4jIPjuGiTw==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/array-float32": "^0.2.2", "@stdlib/complex-float32-ctor": "^0.0.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/complex-float64": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/@stdlib/complex-float64/-/complex-float64-0.2.1.tgz", "integrity": "sha512-vN9GqlSaonoREf8/RIN9tfNLnkfN4s7AI0DPsGnvc1491oOqq9UqMw8rYTrnxuum9/OaNAAUqDkb5GLu5uTveQ==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/assert-is-number": "^0.2.1", "@stdlib/complex-float32": "^0.2.1", "@stdlib/string-format": "^0.2.1", "@stdlib/utils-define-nonenumerable-read-only-property": "^0.2.1", "@stdlib/utils-define-property": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/complex-float64-ctor": {"version": "0.0.3", "resolved": "https://registry.npmjs.org/@stdlib/complex-float64-ctor/-/complex-float64-ctor-0.0.3.tgz", "integrity": "sha512-oixCtBif+Uab2rKtgedwQTbQTEC+wVSu4JQH935eJ8Jo0eL6vXUHHlVrkLgYKlCDLvq5px1QQn42Czg/ixh6Gw==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/assert-is-number": "^0.2.2", "@stdlib/complex-float32-ctor": "^0.0.2", "@stdlib/error-tools-fmtprodmsg": "^0.2.2", "@stdlib/string-format": "^0.2.2", "@stdlib/utils-define-nonenumerable-read-only-property": "^0.2.2", "@stdlib/utils-define-property": "^0.2.4"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/complex-float64-reim": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/@stdlib/complex-float64-reim/-/complex-float64-reim-0.1.2.tgz", "integrity": "sha512-q6RnfgbUunApAYuGmkft1oOM3x3xVMVJwNRlRgfIXwKDb8pYt+S/CeIwi3Su5SF6ay3AqA1s+ze7m21osXAJyw==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/array-float64": "^0.2.2", "@stdlib/complex-float64-ctor": "^0.0.3"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/complex-reim": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/@stdlib/complex-reim/-/complex-reim-0.2.1.tgz", "integrity": "sha512-67nakj+HwBRx/ha3j/sLbrMr2hwFVgEZtaczOgn1Jy/cU03lKvNbMkR7QI9s+sA+b+A3yJB3ob8ZQSqh3D1+dA==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/array-float64": "^0.2.1", "@stdlib/complex-float64": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/complex-reimf": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/@stdlib/complex-reimf/-/complex-reimf-0.2.1.tgz", "integrity": "sha512-6HyPPmo0CEHoBjOg2w70mMFLcFEunM78ljnW6kf1OxjM/mqMaBM1NRpDrQoFwCIdh1RF1ojl3JR0YLllEf0qyQ==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/array-float32": "^0.2.1", "@stdlib/complex-float32": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/constants-float32-max": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/constants-float32-max/-/constants-float32-max-0.2.2.tgz", "integrity": "sha512-uxvIm/KmIeZP4vyfoqPd72l5/uidnCN9YJT3p7Z2LD8hYN3PPLu6pd/5b51HMFLwfkZ27byRJ9+YK6XnneJP0Q==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/constants-float32-smallest-normal": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/constants-float32-smallest-normal/-/constants-float32-smallest-normal-0.2.2.tgz", "integrity": "sha512-2qkGjGML2/8P9YguHnac2AKXLbfycpYdCxKmuXQdAVzMMNCJWjHoIqZMFG29WBEDBOP057X+48S6WhIqoxRpWA==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/constants-float64-e": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/constants-float64-e/-/constants-float64-e-0.2.2.tgz", "integrity": "sha512-7fxHaABwosbUzpBsw6Z9Dd9MqUYne8x+44EjohVcWDr0p0mHB/DXVYEYTlwEP/U/XbRrKdO3jUG6IO/GsEjzWg==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/constants-float64-eps": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/constants-float64-eps/-/constants-float64-eps-0.2.2.tgz", "integrity": "sha512-61Pb2ip9aPhHXxiCn+VZ0UVw2rMYUp0xrX93FXyB3UTLacrofRKLMKtbV0SFac4VXx5igv2+0G+h6G/fwCgjyw==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/constants-float64-eulergamma": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/constants-float64-eulergamma/-/constants-float64-eulergamma-0.2.2.tgz", "integrity": "sha512-XsuVud0d1hLTQspFzgUSH2e3IawTXLlJi2k4Vg0Nn6juulxfNO9PnAGtHz+p1BynYF/YwN+qhKnISQxrN31rsQ==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/constants-float64-exponent-bias": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/constants-float64-exponent-bias/-/constants-float64-exponent-bias-0.2.2.tgz", "integrity": "sha512-zLWkjzDYHSsBsXB/4mwHysOGl64JS3XBt/McjvjCLc/IZpfsUNFxLCl7oVCplXzYYHcQj/RfEBFy6cxQ6FvdpQ==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/constants-float64-fourth-pi": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/constants-float64-fourth-pi/-/constants-float64-fourth-pi-0.2.2.tgz", "integrity": "sha512-j0N<PERSON>g45ouibms4ML8pfS/eDrurdtnhJTNPCGQM4mg3X+1ljsuO0pvkpVCvuz29t5J23KTcfGBXXr90ikoBmjlw==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/constants-float64-gamma-lanczos-g": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/constants-float64-gamma-lanczos-g/-/constants-float64-gamma-lanczos-g-0.2.2.tgz", "integrity": "sha512-hCaZbZ042htCy9mlGrfUEbz4d0xW/DLdr3vHs5KiBWU+G+WHVH33vubSnEoyT0ugWpAk2ZqWXe/V8sLGgOu0xg==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/constants-float64-half-ln-two": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/constants-float64-half-ln-two/-/constants-float64-half-ln-two-0.2.2.tgz", "integrity": "sha512-yv1XhzZR2AfJmnAGL0kdWlIUhc/vqdWol+1Gq2brXPVfgqbUmJu5XZuuK+jZA2k+fHyvRHNEwQRv9OPnOjchFg==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/constants-float64-half-pi": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/constants-float64-half-pi/-/constants-float64-half-pi-0.2.2.tgz", "integrity": "sha512-lM3SiDsZCKiuF5lPThZFFqioIwh1bUiBUnnDMLB04/QkVRCAaXUo+dsq2hOB6iBhHoYhiKds6T+PsHSBlpqAaA==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/constants-float64-high-word-abs-mask": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/constants-float64-high-word-abs-mask/-/constants-float64-high-word-abs-mask-0.2.2.tgz", "integrity": "sha512-YtYngcHlw9qvOpmsSlkNHi6cy/7Y7QkyYh5kJbDvuOUXPDKa3rEwBln4mKjbWsXhmmN0bk7TLypH7Ryd/UAjUQ==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/constants-float64-high-word-exponent-mask": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/constants-float64-high-word-exponent-mask/-/constants-float64-high-word-exponent-mask-0.2.2.tgz", "integrity": "sha512-LhYUXvpnLOFnWr8ucHA9N/H75VxcS2T9EoBDTmWBZoKj2Pg0icGVDmcNciRLIWbuPA9osgcKpxoU+ADIfaipVA==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/constants-float64-high-word-sign-mask": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/@stdlib/constants-float64-high-word-sign-mask/-/constants-float64-high-word-sign-mask-0.2.1.tgz", "integrity": "sha512-Fep/Ccgvz5i9d5k96zJsDjgXGno8HJfmH7wihLmziFmA2z9t7NSacH4/BH4rPJ5yXFHLkacNLDxaF1gO1XpcLA==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/constants-float64-high-word-significand-mask": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/constants-float64-high-word-significand-mask/-/constants-float64-high-word-significand-mask-0.2.2.tgz", "integrity": "sha512-eDDyiQ5PR1/qyklrW0Pus0ZopM7BYjkWTjqhSHhj0DibH6UMwSMlIl4ddCh3VX37p5eByuAavnaPgizk5c9mUw==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/constants-float64-ln-sqrt-two-pi": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/constants-float64-ln-sqrt-two-pi/-/constants-float64-ln-sqrt-two-pi-0.2.2.tgz", "integrity": "sha512-C9YS9W/lvv54wUC7DojQSRH9faKw0sMAM09oMRVm8OOYNr01Rs1wXeSPStl9ns4qiV/G13vZzd1I3nGqgqihbw==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/constants-float64-ln-two": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/constants-float64-ln-two/-/constants-float64-ln-two-0.2.2.tgz", "integrity": "sha512-EQ8EJ6B1wPfuhva0aApKIsF7lTna++txV4AUzL2wTfwDHw6RzWpA44u+k54KnLF8ZXUNIYDNQHHvtzdfKrFzCA==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/constants-float64-max": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/constants-float64-max/-/constants-float64-max-0.2.2.tgz", "integrity": "sha512-S3kcIKTK65hPqirziof3KTYqfFKopgaTnaiDlDKdzaCzBZ5qkrAcRd4vl+W1KHoZruUyWC2/RYZUa/8+h075TQ==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/constants-float64-max-base2-exponent": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/constants-float64-max-base2-exponent/-/constants-float64-max-base2-exponent-0.2.2.tgz", "integrity": "sha512-KmDe98pJ2HXz2SbqyFfSDhlSSVD7JssjbZ5K11HEK2avqMcoCbdHH20T+6/TpA01VqaK8dLbeyphOfALcDdMKA==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/constants-float64-max-base2-exponent-subnormal": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/@stdlib/constants-float64-max-base2-exponent-subnormal/-/constants-float64-max-base2-exponent-subnormal-0.2.1.tgz", "integrity": "sha512-D1wBNn54Hu2pK6P/yBz0FtPBI3/7HdgK8igYjWDKWUKzC92R/6PHZ9q5NzedcGxoBs8MUk1zNpP0tZyYj9Y4YQ==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/constants-float64-max-ln": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/constants-float64-max-ln/-/constants-float64-max-ln-0.2.2.tgz", "integrity": "sha512-FPAEGjnoQMDPWJbCyyto7HWQ/SY2jjD8IkjyD8aOwENqbswjCbOINXRiK2ar27OOXG7Dv7CCpFpoorTxv0gmfA==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/constants-float64-max-safe-integer": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/constants-float64-max-safe-integer/-/constants-float64-max-safe-integer-0.2.2.tgz", "integrity": "sha512-d+sxmxhkt980SDFhnnRDSpujPQTv4nEt5Ox3L86HgYZU4mQU/wbzYVkMuHIANW9x3ehww5blnGXTKYG9rQCXAw==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/constants-float64-max-safe-nth-factorial": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/@stdlib/constants-float64-max-safe-nth-factorial/-/constants-float64-max-safe-nth-factorial-0.1.0.tgz", "integrity": "sha512-sppIfkBbeyKNwfRbmNFi5obI7Q+IJCQzfWKYqvzmEJVOkmEg6hhtEeFc8zZJGCU7+Pndc3M2wdbTT5a3rhamHw==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/constants-float64-min-base2-exponent": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/constants-float64-min-base2-exponent/-/constants-float64-min-base2-exponent-0.2.2.tgz", "integrity": "sha512-YZmBiKik6LbWB4EOZ/ZUs/u6OIF742xNK8mhEqL0OEN4NuJe3OdErpOic6KjMmHjQuqCXdFoSqsWZaFHcIN7HA==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/constants-float64-min-base2-exponent-subnormal": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/@stdlib/constants-float64-min-base2-exponent-subnormal/-/constants-float64-min-base2-exponent-subnormal-0.2.1.tgz", "integrity": "sha512-fTXfvctXWj/48gK+gbRBrHuEHEKY4QOJoXSGp414Sz6vUxHusHJJ686p8ze3XqM7CY6fmL09ZgdGz/uhJl/7lw==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/constants-float64-min-ln": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/constants-float64-min-ln/-/constants-float64-min-ln-0.2.2.tgz", "integrity": "sha512-N1Sxjo3uTdEIpHeG2TzaX06UuvpcKHvjYKpIMhJSajbxvfVDURHlc9kIpfbP9C9/YYoCy0FvewA/kvbqNaYypA==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/constants-float64-ninf": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/constants-float64-ninf/-/constants-float64-ninf-0.2.2.tgz", "integrity": "sha512-Iu+wZs/vgudAKVg9FEcRY3FadkmvsWuq/wJ3jIHjhaP5xcnoF3XJUO4IneEndybHwehfJL65NShnDsJcg1gicw==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/number-ctor": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/constants-float64-pi": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/constants-float64-pi/-/constants-float64-pi-0.2.2.tgz", "integrity": "sha512-ix34KmpUQ0LUM++L6avLhM9LFCcGTlsUDyWD/tYVGZBiIzDS3TMKShHRkZvC+v87fuyYNPoxolYtk5AlbacI6g==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/constants-float64-pinf": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/constants-float64-pinf/-/constants-float64-pinf-0.2.2.tgz", "integrity": "sha512-UcwnWaSkUMD8QyKADwkXPlY7yOosCPZpE2EDXf/+WOzuWi5vpsec+JaasD5ggAN8Rv8OTVmexTFs1uZfrHgqVQ==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/constants-float64-smallest-normal": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/constants-float64-smallest-normal/-/constants-float64-smallest-normal-0.2.2.tgz", "integrity": "sha512-GXNBkdqLT9X+dU59O1kmb7W5da/RhSXSvxx0xG5r7ipJPOtRLfTXGGvvTzWD4xA3Z5TKlrEL6ww5sph9BsPJnA==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/constants-float64-smallest-subnormal": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/constants-float64-smallest-subnormal/-/constants-float64-smallest-subnormal-0.2.2.tgz", "integrity": "sha512-Ku<PERSON>+scDOsP0okx8RLF+q3l1RheaYChf+u/HbhzFbz82GeCIdIVp86UMwoBgfn8AT8cnR5SrtvLtQw15MGfa/vg==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/constants-float64-sqrt-eps": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/constants-float64-sqrt-eps/-/constants-float64-sqrt-eps-0.2.2.tgz", "integrity": "sha512-X7LnGfnwNnhiwlY+zd3FX6zclsx61MaboGTNAAdaV78YjBDTdGdWMHk5MQo1U17ryPlhdGphOAejhDHeaSnTXQ==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/constants-float64-sqrt-two": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/constants-float64-sqrt-two/-/constants-float64-sqrt-two-0.2.2.tgz", "integrity": "sha512-iqqouCuS9pUhjD91i5siScxLDtQTF1HsSZor6jaZRviMiOjCj/mjzxxTFHWUlU/rxHMBBhj/u7i12fv6a7dCAQ==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/constants-float64-sqrt-two-pi": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/constants-float64-sqrt-two-pi/-/constants-float64-sqrt-two-pi-0.2.2.tgz", "integrity": "sha512-I8Ylr64x8AFSQ2hFBT8szuIBAy2wqPx69taJMzfcmuM5SnSbS8SE/H19YnCimZErVFo4bz0Rh8Fp3edN4i6teQ==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/constants-float64-two-pi": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/constants-float64-two-pi/-/constants-float64-two-pi-0.2.2.tgz", "integrity": "sha512-cyXuwYOersVsA8tDSJ0ocMbtOc5KGxjlGvYC4vrpLQVkgNpxcGbA57n6JvaGmNk7+InXXbQ7qhTWGbTNgafcLQ==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/constants-int32-max": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/@stdlib/constants-int32-max/-/constants-int32-max-0.3.0.tgz", "integrity": "sha512-jYN84QfG/yP2RYw98OR6UYehFFs0PsGAihV6pYU0ey+WF9IOXgSjRP56KMoZ7ctHwl4wsnj9I+qB2tGuEXr+pQ==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/constants-uint16-max": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/constants-uint16-max/-/constants-uint16-max-0.2.2.tgz", "integrity": "sha512-qaFXbxgFnAkt73P5Ch7ODb0TsOTg0LEBM52hw6qt7+gTMZUdS0zBAiy5J2eEkTxA9rD9X3nIyUtLf2C7jafNdw==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/constants-uint32-max": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/constants-uint32-max/-/constants-uint32-max-0.2.2.tgz", "integrity": "sha512-2G44HQgIKDrh3tJUkmvtz+eM+uwDvOMF+2I3sONcTHacANb+zP7la4LDYiTp+HFkPJyfh/kPapXBiHpissAb1A==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/constants-uint8-max": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/constants-uint8-max/-/constants-uint8-max-0.2.2.tgz", "integrity": "sha512-ZTBQq3fqS/Y4ll6cPY5SKaS266EfmKP9PW3YLJaTELmYIzVo9w2RFtfCqN05G3olTQ6Le9MUEE/C6VFgZNElDQ==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/error-tools-fmtprodmsg": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/error-tools-fmtprodmsg/-/error-tools-fmtprodmsg-0.2.2.tgz", "integrity": "sha512-2IliQfTes4WV5odPidZFGD5eYDswZrPXob7oOu95Q69ERqImo8WzSwnG2EDbHPyOyYCewuMfM5Ha6Ggf+u944Q==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/fs-exists": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/fs-exists/-/fs-exists-0.2.2.tgz", "integrity": "sha512-uGLqc7izCIam2aTyv0miyktl4l8awgRkCS39eIEvvvnKIaTBF6pxfac7FtFHeEQKE3XhtKsOmdQ/yJjUMChLuA==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/utils-define-nonenumerable-read-only-property": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/fs-resolve-parent-path": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/fs-resolve-parent-path/-/fs-resolve-parent-path-0.2.2.tgz", "integrity": "sha512-ZG78ouZc+pdPLtU+sSpYTvbKTiLUgn6NTtlVFYmcmkYRFn+fGOOakwVuhYMcYG6ti10cLD6WzB/YujxIt8f+nA==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/assert-has-own-property": "^0.2.2", "@stdlib/assert-is-function": "^0.2.2", "@stdlib/assert-is-plain-object": "^0.2.2", "@stdlib/assert-is-string": "^0.2.2", "@stdlib/error-tools-fmtprodmsg": "^0.2.2", "@stdlib/fs-exists": "^0.2.2", "@stdlib/process-cwd": "^0.2.2", "@stdlib/string-format": "^0.2.2", "@stdlib/utils-define-nonenumerable-read-only-property": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/function-ctor": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/function-ctor/-/function-ctor-0.2.2.tgz", "integrity": "sha512-qSn1XQnnhgCSYBfFy4II0dY5eW4wdOprgDTHcOJ3PkPWuZHDC1fXZsok1OYAosHqIiIw44zBFcMS/JRex4ebdQ==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-assert-is-even": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/@stdlib/math-base-assert-is-even/-/math-base-assert-is-even-0.2.3.tgz", "integrity": "sha512-cziGv8F/aNyfME7Wx2XJjnYBnf9vIeh8yTIzlLELd0OqGHqfsHU5OQxxcl9x5DbjZ1G/w0lphWxHFHYCuwFCHw==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/math-base-assert-is-integer": "^0.2.4", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-assert-is-infinite": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/math-base-assert-is-infinite/-/math-base-assert-is-infinite-0.2.2.tgz", "integrity": "sha512-4zDZuinC3vkXRdQepr0ZTwWX3KgM0VIWqYthOmCSgLLA87L9M9z9MgUZL1QeYeYa0+60epjDcQ8MS3ecT70Jxw==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float64-ninf": "^0.2.2", "@stdlib/constants-float64-pinf": "^0.2.1", "@stdlib/utils-library-manifest": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-assert-is-integer": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/@stdlib/math-base-assert-is-integer/-/math-base-assert-is-integer-0.2.5.tgz", "integrity": "sha512-Zi8N66GbWtSCR3OUsRdBknjNlX+aBN8w6CaVEP5+Jy/a7MgMYzevS52TNS5sm8jqzKBlFhZlPLex+Zl2GlPvSA==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/math-base-special-floor": "^0.2.3", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-assert-is-nan": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/math-base-assert-is-nan/-/math-base-assert-is-nan-0.2.2.tgz", "integrity": "sha512-QVS8rpWdkR9YmHqiYLDVLsCiM+dASt/2feuTl4T/GSdou3Y/PS/4j/tuDvCDoHDNfDkULUW+FCVjKYpbyoeqBQ==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/utils-library-manifest": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-assert-is-negative-zero": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/math-base-assert-is-negative-zero/-/math-base-assert-is-negative-zero-0.2.2.tgz", "integrity": "sha512-WvKNuBZ6CDarOTzOuFLmO1jwZnFD+butIvfD2Ws6SsuqSCiWOaF4OhIckqPzo1XEdkqqhRNPqBxqc0D+hsEYVA==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float64-ninf": "^0.2.2", "@stdlib/utils-library-manifest": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-assert-is-odd": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/@stdlib/math-base-assert-is-odd/-/math-base-assert-is-odd-0.3.0.tgz", "integrity": "sha512-V44F3xdR5/bHXqqYvE/AldLnVmijLr/rgf7EjnJXXDQLfPCgemy0iHTFl19N68KG1YO9SMPdyOaNjh4K0O9Qqw==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/math-base-assert-is-even": "^0.2.2", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-assert-is-positive-zero": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/math-base-assert-is-positive-zero/-/math-base-assert-is-positive-zero-0.2.2.tgz", "integrity": "sha512-mMX5xsemKpHRAgjpVJCb3eVZ3WIkZh6KnHQH8i8n4vI44pcdpN5rcTdEAMlhLjxT/rT7H2wq85f7/FRsq9r9rw==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float64-pinf": "^0.2.1", "@stdlib/utils-library-manifest": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-napi-binary": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/@stdlib/math-base-napi-binary/-/math-base-napi-binary-0.3.0.tgz", "integrity": "sha512-bhwsmGMOMN1srcpNAFRjDMSXe9ue1s/XmaoBBlqcG6S2nqRQlIVnKKH4WZx4hmC1jDqoFXuNPJGE47VXpVV+mA==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/complex-float32-ctor": "^0.0.2", "@stdlib/complex-float32-reim": "^0.1.2", "@stdlib/complex-float64-ctor": "^0.0.3", "@stdlib/complex-float64-reim": "^0.1.2", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-napi-unary": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/@stdlib/math-base-napi-unary/-/math-base-napi-unary-0.2.3.tgz", "integrity": "sha512-BCyJmpq2S8EFo2yMt1z+v1EL7nn8RHcM6jn7fa8n3BTP679K0MSlawIh3A0CFogfrTdjPM4G44VO1ddsdLExcg==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/complex-float32-ctor": "^0.0.2", "@stdlib/complex-float32-reim": "^0.1.1", "@stdlib/complex-float64-ctor": "^0.0.3", "@stdlib/complex-float64-reim": "^0.1.1", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-abs": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-abs/-/math-base-special-abs-0.2.2.tgz", "integrity": "sha512-cw5CXj05c/L0COaD9J+paHXwmoN5IBYh+Spk0331f1pEMvGxSO1KmCREZaooUEEFKPhKDukEHKeitja2yAQh4Q==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float64-high-word-abs-mask": "^0.2.2", "@stdlib/math-base-napi-unary": "^0.2.3", "@stdlib/number-float64-base-to-words": "^0.2.2", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-acos": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-acos/-/math-base-special-acos-0.2.3.tgz", "integrity": "sha512-f66Ikq0E3U5XQm6sTu4UHwP3TmcPrVgSK/mZTvg2JenswZ6qPtGO1A8KHZ5+/5bk1TSc9EW4zDGUqWG7mGzT4Q==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float64-fourth-pi": "^0.2.2", "@stdlib/math-base-assert-is-nan": "^0.2.2", "@stdlib/math-base-napi-unary": "^0.2.3", "@stdlib/math-base-special-asin": "^0.2.2", "@stdlib/math-base-special-sqrt": "^0.2.2", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-asin": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-asin/-/math-base-special-asin-0.2.3.tgz", "integrity": "sha512-Ju1UFJspOOL630SqBtVmUh3lHv5JMu1szcAgx7kQupJwZiwWljoVQ5MmxlNY4l3nGM5oMokenlqTDNXOau43lw==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float64-fourth-pi": "^0.2.2", "@stdlib/math-base-assert-is-nan": "^0.2.2", "@stdlib/math-base-napi-unary": "^0.2.3", "@stdlib/math-base-special-sqrt": "^0.2.2", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-beta": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-beta/-/math-base-special-beta-0.2.1.tgz", "integrity": "sha512-/crN/ptCu7ld7KodGkYUJIweUTHdxO5mw+rgkrMqNVqJ83QQPd1czB6hvNYFLfmhy3ckj7t/UYoYhhg/x/Wd7g==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float64-e": "^0.2.1", "@stdlib/constants-float64-eps": "^0.2.1", "@stdlib/math-base-assert-is-nan": "^0.2.1", "@stdlib/math-base-special-abs": "^0.2.1", "@stdlib/math-base-special-exp": "^0.2.1", "@stdlib/math-base-special-log1p": "^0.2.1", "@stdlib/math-base-special-pow": "^0.2.1", "@stdlib/math-base-special-sqrt": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-betainc": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-betainc/-/math-base-special-betainc-0.2.2.tgz", "integrity": "sha512-95tzDgn5d9RV9al4gxHwKfszd9M6AizlpnhAiwIi0JwqcO+OY3xgbABWal4/H09Tb8DaC9jDqiyGuyPuB0iDew==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/math-base-special-kernel-betainc": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-binomcoef": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-binomcoef/-/math-base-special-binomcoef-0.2.3.tgz", "integrity": "sha512-RxnQ/QGgKUeqTvBL+7IH8rNKQYCfGs0I3PsFYfb0e9V1O2yIVvthURUpzjukurZM89JRapK1dN6aeZ5UM71Zgw==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float64-max-safe-integer": "^0.2.2", "@stdlib/constants-float64-pinf": "^0.2.2", "@stdlib/math-base-assert-is-integer": "^0.2.5", "@stdlib/math-base-assert-is-nan": "^0.2.2", "@stdlib/math-base-assert-is-odd": "^0.3.0", "@stdlib/math-base-special-floor": "^0.2.3", "@stdlib/math-base-special-gcd": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-ceil": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-ceil/-/math-base-special-ceil-0.2.2.tgz", "integrity": "sha512-zGkDaMcPrxQ9Zo+fegf2MyI8UPIrVTK5sc/FgCN9qdwEFJTKGLsBd249T3xH7L2MDxx5JbIMGrr6L4U4uEm2Hw==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/math-base-napi-unary": "^0.2.3", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-copysign": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-copysign/-/math-base-special-copysign-0.2.2.tgz", "integrity": "sha512-m9nWIQhKsaNrZtS2vIPeToWDbzs/T0d0NWy7gSci38auQVufSbF6FYnCKl0f+uwiWlh5GYXs0uVbyCp7FFXN+A==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float64-high-word-abs-mask": "^0.2.2", "@stdlib/constants-float64-high-word-sign-mask": "^0.2.1", "@stdlib/math-base-napi-binary": "^0.3.0", "@stdlib/number-float64-base-from-words": "^0.2.2", "@stdlib/number-float64-base-get-high-word": "^0.2.2", "@stdlib/number-float64-base-to-words": "^0.2.2", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-cos": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-cos/-/math-base-special-cos-0.2.1.tgz", "integrity": "sha512-Yre+ASwsv4pQJk5dqY6488ZfmYDA6vtUTdapAVjCx28NluSFhXw1+S8EmsqnzYnqp/4x7Y1H7V2UPZfw+AdnbQ==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/math-base-special-kernel-cos": "^0.2.1", "@stdlib/math-base-special-kernel-sin": "^0.2.1", "@stdlib/math-base-special-rempio2": "^0.2.1", "@stdlib/number-float64-base-get-high-word": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-erfc": {"version": "0.2.4", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-erfc/-/math-base-special-erfc-0.2.4.tgz", "integrity": "sha512-tVI+mMnW+oDfQXwoH86sZ8q4ximpUXX6wZFCYZB6KcO5GXeKuvK74DnU0YyIm+sTV+r9WJiTSBEW9iVQLZOkzg==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float64-ninf": "^0.2.2", "@stdlib/constants-float64-pinf": "^0.2.2", "@stdlib/math-base-assert-is-nan": "^0.2.2", "@stdlib/math-base-napi-unary": "^0.2.3", "@stdlib/math-base-special-exp": "^0.2.4", "@stdlib/number-float64-base-set-low-word": "^0.2.2", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-erfcinv": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-erfcinv/-/math-base-special-erfcinv-0.2.3.tgz", "integrity": "sha512-B8u7WZiIh0+rX8VWNOwvjPWpmeKBHIQoJtIigUseBgbch/rmgV43k63MCkjh2u+V2SmcFo38yD94qJg5bYyWeA==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float64-ninf": "^0.2.2", "@stdlib/constants-float64-pinf": "^0.2.2", "@stdlib/math-base-assert-is-nan": "^0.2.2", "@stdlib/math-base-napi-unary": "^0.2.3", "@stdlib/math-base-special-ln": "^0.2.4", "@stdlib/math-base-special-sqrt": "^0.2.2", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-exp": {"version": "0.2.4", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-exp/-/math-base-special-exp-0.2.4.tgz", "integrity": "sha512-G6pZqu1wA4WwBj7DcnztA+/ro61wXJUTpKFLOwrIb2f/28pHGpA//Lub+3vAk6/ksAkhJ+qM/dfdM2ue7zLuEw==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float64-ninf": "^0.2.2", "@stdlib/constants-float64-pinf": "^0.2.2", "@stdlib/math-base-assert-is-nan": "^0.2.2", "@stdlib/math-base-napi-unary": "^0.2.3", "@stdlib/math-base-special-ldexp": "^0.2.3", "@stdlib/math-base-special-trunc": "^0.2.2", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-expm1": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-expm1/-/math-base-special-expm1-0.2.3.tgz", "integrity": "sha512-uJlYZjPjG9X8owuwp1h1/iz9xf21v3dlyEAuutQ0NoacUDzZKVSCbQ3Of0i2Mujn+4N+kjCvEeph6cqhfYAl+A==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float64-exponent-bias": "^0.2.2", "@stdlib/constants-float64-half-ln-two": "^0.2.2", "@stdlib/constants-float64-ninf": "^0.2.2", "@stdlib/constants-float64-pinf": "^0.2.2", "@stdlib/math-base-assert-is-nan": "^0.2.2", "@stdlib/math-base-napi-unary": "^0.2.3", "@stdlib/number-float64-base-from-words": "^0.2.2", "@stdlib/number-float64-base-get-high-word": "^0.2.2", "@stdlib/number-float64-base-set-high-word": "^0.2.2", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-factorial": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-factorial/-/math-base-special-factorial-0.2.1.tgz", "integrity": "sha512-uqsANeW4gHFzhgDrV9X0INEwO74MPzQvDVXbxY9+b0E13Vq2HHCi0GqdtPOWXdhOCUk8RkLRs9GizU3X6Coy8A==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float64-pinf": "^0.2.1", "@stdlib/math-base-assert-is-integer": "^0.2.1", "@stdlib/math-base-assert-is-nan": "^0.2.1", "@stdlib/math-base-special-gamma": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-floor": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-floor/-/math-base-special-floor-0.2.3.tgz", "integrity": "sha512-zTkxVRawtWwJ4NmAT/1e+ZsIoBj1JqUquGOpiNVGNIKtyLOeCONZlZSbN7zuxPkshvmcSjpQ/VLKR8Tw/37E9A==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/math-base-napi-unary": "^0.2.1", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-fmod": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-fmod/-/math-base-special-fmod-0.1.0.tgz", "integrity": "sha512-osHwmEOT5MPWOXRx8y3wKCp362eGHIcJRt8LARJJICr/qTZlu1HMnZnbwuhfy1NIQzpJ8aLOhEdl2PrProTt3A==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float64-exponent-bias": "^0.2.2", "@stdlib/constants-float64-high-word-abs-mask": "^0.2.2", "@stdlib/constants-float64-high-word-exponent-mask": "^0.2.2", "@stdlib/constants-float64-high-word-sign-mask": "^0.2.1", "@stdlib/constants-float64-high-word-significand-mask": "^0.2.2", "@stdlib/constants-float64-min-base2-exponent": "^0.2.2", "@stdlib/math-base-napi-binary": "^0.3.0", "@stdlib/number-float64-base-from-words": "^0.2.2", "@stdlib/number-float64-base-to-words": "^0.2.2", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-gamma": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-gamma/-/math-base-special-gamma-0.2.1.tgz", "integrity": "sha512-Sfq1HnVoL4kN9EDHH3YparEAF0r7QD5jNFppUTOXmrqkofgImSl5tLttttnr2I7O9zsNhYkBAiTx9q0y25bAiA==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float64-eulergamma": "^0.2.1", "@stdlib/constants-float64-ninf": "^0.2.1", "@stdlib/constants-float64-pi": "^0.2.1", "@stdlib/constants-float64-pinf": "^0.2.1", "@stdlib/constants-float64-sqrt-two-pi": "^0.2.1", "@stdlib/math-base-assert-is-integer": "^0.2.1", "@stdlib/math-base-assert-is-nan": "^0.2.1", "@stdlib/math-base-assert-is-negative-zero": "^0.2.1", "@stdlib/math-base-special-abs": "^0.2.1", "@stdlib/math-base-special-exp": "^0.2.1", "@stdlib/math-base-special-floor": "^0.2.1", "@stdlib/math-base-special-pow": "^0.2.1", "@stdlib/math-base-special-sin": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-gamma-delta-ratio": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-gamma-delta-ratio/-/math-base-special-gamma-delta-ratio-0.2.2.tgz", "integrity": "sha512-lan+cfafH7aoyUxa88vLO+pYwLA+0uiyVFmCumxDemQUboCrTiNCYhBjONFGI/ljE3RukHoE3ZV4AccIcx526A==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float64-e": "^0.2.2", "@stdlib/constants-float64-eps": "^0.2.2", "@stdlib/constants-float64-gamma-lanczos-g": "^0.2.2", "@stdlib/math-base-special-abs": "^0.2.2", "@stdlib/math-base-special-exp": "^0.2.4", "@stdlib/math-base-special-factorial": "^0.2.1", "@stdlib/math-base-special-floor": "^0.2.3", "@stdlib/math-base-special-gamma": "^0.2.1", "@stdlib/math-base-special-gamma-lanczos-sum": "^0.3.0", "@stdlib/math-base-special-log1p": "^0.2.3", "@stdlib/math-base-special-pow": "^0.3.0"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-gamma-delta-ratio/node_modules/@stdlib/math-base-special-pow": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-pow/-/math-base-special-pow-0.3.0.tgz", "integrity": "sha512-sMDYRUYGFyMXDHcCYy7hE07lV7jgI6rDspLMROKyESWcH4n8j54XE4/0w0i8OpdzR40H895MaPWU/tVnU1tP6w==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float64-exponent-bias": "^0.2.2", "@stdlib/constants-float64-high-word-abs-mask": "^0.2.2", "@stdlib/constants-float64-high-word-significand-mask": "^0.2.2", "@stdlib/constants-float64-ln-two": "^0.2.2", "@stdlib/constants-float64-ninf": "^0.2.2", "@stdlib/constants-float64-pinf": "^0.2.2", "@stdlib/math-base-assert-is-infinite": "^0.2.2", "@stdlib/math-base-assert-is-integer": "^0.2.5", "@stdlib/math-base-assert-is-nan": "^0.2.2", "@stdlib/math-base-assert-is-odd": "^0.3.0", "@stdlib/math-base-napi-binary": "^0.3.0", "@stdlib/math-base-special-abs": "^0.2.2", "@stdlib/math-base-special-copysign": "^0.2.1", "@stdlib/math-base-special-ldexp": "^0.2.2", "@stdlib/math-base-special-sqrt": "^0.2.2", "@stdlib/number-float64-base-get-high-word": "^0.2.2", "@stdlib/number-float64-base-set-high-word": "^0.2.2", "@stdlib/number-float64-base-set-low-word": "^0.2.2", "@stdlib/number-float64-base-to-words": "^0.2.2", "@stdlib/number-uint32-base-to-int32": "^0.2.2", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-gamma-lanczos-sum": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-gamma-lanczos-sum/-/math-base-special-gamma-lanczos-sum-0.3.0.tgz", "integrity": "sha512-q13p6r7G0TmbD54cU8QgG8wGgdGGznV9dNKiNszw+hOqCQ+1DqziG8I6vN64R3EQLP7QN4yVprZcmuXSK+fgsg==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float64-pinf": "^0.2.2", "@stdlib/math-base-napi-unary": "^0.2.1", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-gamma-lanczos-sum-expg-scaled": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-gamma-lanczos-sum-expg-scaled/-/math-base-special-gamma-lanczos-sum-expg-scaled-0.3.0.tgz", "integrity": "sha512-hScjKZvueOK5piX84ZLIV3ZiYvtvYtcixN8psxkPIxJlN7Bd5nAmSkEOBL+T+LeW2RjmdEMXFFJMF7FsK1js/Q==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/math-base-napi-unary": "^0.2.1", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-gamma1pm1": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-gamma1pm1/-/math-base-special-gamma1pm1-0.2.2.tgz", "integrity": "sha512-lNT1lk0ifK2a/ta3GfR5V8KvfgkgheE44n5AQ/07BBfcVBMiAdqNuyjSMeWqsH/zVGzjU6G8+kLBzmaJXivPXQ==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float64-eps": "^0.2.2", "@stdlib/math-base-assert-is-nan": "^0.2.2", "@stdlib/math-base-special-expm1": "^0.2.3", "@stdlib/math-base-special-gamma": "^0.2.1", "@stdlib/math-base-special-ln": "^0.2.4", "@stdlib/math-base-special-log1p": "^0.2.3"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-gammainc": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-gammainc/-/math-base-special-gammainc-0.2.2.tgz", "integrity": "sha512-ffKZFiv/41SXs2Xms7IW3lPnICR898yfWAidq5uKjOLgRb3wrzNjq0sZ6EAVXvdBwyGULvSjyud28PpVhDLv3A==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float64-e": "^0.2.2", "@stdlib/constants-float64-gamma-lanczos-g": "^0.2.2", "@stdlib/constants-float64-max": "^0.2.2", "@stdlib/constants-float64-max-ln": "^0.2.2", "@stdlib/constants-float64-min-ln": "^0.2.2", "@stdlib/constants-float64-pi": "^0.2.2", "@stdlib/constants-float64-pinf": "^0.2.2", "@stdlib/constants-float64-sqrt-eps": "^0.2.2", "@stdlib/constants-float64-sqrt-two-pi": "^0.2.2", "@stdlib/constants-float64-two-pi": "^0.2.2", "@stdlib/math-base-special-abs": "^0.2.2", "@stdlib/math-base-special-erfc": "^0.2.4", "@stdlib/math-base-special-exp": "^0.2.4", "@stdlib/math-base-special-floor": "^0.2.3", "@stdlib/math-base-special-gamma": "^0.3.0", "@stdlib/math-base-special-gamma-lanczos-sum-expg-scaled": "^0.3.0", "@stdlib/math-base-special-gamma1pm1": "^0.2.2", "@stdlib/math-base-special-gammaln": "^0.2.2", "@stdlib/math-base-special-ln": "^0.2.4", "@stdlib/math-base-special-log1p": "^0.2.3", "@stdlib/math-base-special-log1pmx": "^0.2.3", "@stdlib/math-base-special-max": "^0.3.0", "@stdlib/math-base-special-min": "^0.2.3", "@stdlib/math-base-special-pow": "^0.3.0", "@stdlib/math-base-special-powm1": "^0.3.0", "@stdlib/math-base-special-sqrt": "^0.2.2", "@stdlib/math-base-tools-continued-fraction": "^0.2.2", "@stdlib/math-base-tools-evalpoly": "^0.2.2", "@stdlib/math-base-tools-sum-series": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-gammainc/node_modules/@stdlib/math-base-special-gamma": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-gamma/-/math-base-special-gamma-0.3.0.tgz", "integrity": "sha512-YfW+e5xuSDoUxgpquXPrFtAbdwOzE7Kqt7M0dcAkDNot8/yUn+QmrDGzURyBVzUyhRm9SaC9bACHxTShdJkcuA==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float64-eulergamma": "^0.2.2", "@stdlib/constants-float64-ninf": "^0.2.2", "@stdlib/constants-float64-pi": "^0.2.2", "@stdlib/constants-float64-pinf": "^0.2.2", "@stdlib/constants-float64-sqrt-two-pi": "^0.2.2", "@stdlib/math-base-assert-is-integer": "^0.2.5", "@stdlib/math-base-assert-is-nan": "^0.2.2", "@stdlib/math-base-assert-is-negative-zero": "^0.2.2", "@stdlib/math-base-napi-unary": "^0.2.3", "@stdlib/math-base-special-abs": "^0.2.2", "@stdlib/math-base-special-exp": "^0.2.4", "@stdlib/math-base-special-floor": "^0.2.3", "@stdlib/math-base-special-pow": "^0.3.0", "@stdlib/math-base-special-sin": "^0.2.1", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-gammainc/node_modules/@stdlib/math-base-special-max": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-max/-/math-base-special-max-0.3.0.tgz", "integrity": "sha512-yXsmdFCLHRB24l34Kn1kHZXHKoGqBxPY/5Mi+n5qLg+FwrX85ZG6KGVbO3DfcpG1NxDTcEKb1hxbUargI0P5fw==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float64-pinf": "^0.2.2", "@stdlib/math-base-assert-is-nan": "^0.2.2", "@stdlib/math-base-assert-is-positive-zero": "^0.2.2", "@stdlib/math-base-napi-binary": "^0.2.1", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-gammainc/node_modules/@stdlib/math-base-special-max/node_modules/@stdlib/math-base-napi-binary": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/@stdlib/math-base-napi-binary/-/math-base-napi-binary-0.2.1.tgz", "integrity": "sha512-ewGarSRaz5gaLsE17yJ4me03e56ICgPAA0ru0SYFCeMK2E5Z4Z2Lbu7HAQTTg+8XhpoaZSw0h2GJopTV7PCKmw==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/complex-float32": "^0.2.1", "@stdlib/complex-float64": "^0.2.1", "@stdlib/complex-reim": "^0.2.1", "@stdlib/complex-reimf": "^0.2.1", "@stdlib/utils-library-manifest": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-gammainc/node_modules/@stdlib/math-base-special-pow": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-pow/-/math-base-special-pow-0.3.0.tgz", "integrity": "sha512-sMDYRUYGFyMXDHcCYy7hE07lV7jgI6rDspLMROKyESWcH4n8j54XE4/0w0i8OpdzR40H895MaPWU/tVnU1tP6w==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float64-exponent-bias": "^0.2.2", "@stdlib/constants-float64-high-word-abs-mask": "^0.2.2", "@stdlib/constants-float64-high-word-significand-mask": "^0.2.2", "@stdlib/constants-float64-ln-two": "^0.2.2", "@stdlib/constants-float64-ninf": "^0.2.2", "@stdlib/constants-float64-pinf": "^0.2.2", "@stdlib/math-base-assert-is-infinite": "^0.2.2", "@stdlib/math-base-assert-is-integer": "^0.2.5", "@stdlib/math-base-assert-is-nan": "^0.2.2", "@stdlib/math-base-assert-is-odd": "^0.3.0", "@stdlib/math-base-napi-binary": "^0.3.0", "@stdlib/math-base-special-abs": "^0.2.2", "@stdlib/math-base-special-copysign": "^0.2.1", "@stdlib/math-base-special-ldexp": "^0.2.2", "@stdlib/math-base-special-sqrt": "^0.2.2", "@stdlib/number-float64-base-get-high-word": "^0.2.2", "@stdlib/number-float64-base-set-high-word": "^0.2.2", "@stdlib/number-float64-base-set-low-word": "^0.2.2", "@stdlib/number-float64-base-to-words": "^0.2.2", "@stdlib/number-uint32-base-to-int32": "^0.2.2", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-gammaincinv": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-gammaincinv/-/math-base-special-gammaincinv-0.2.2.tgz", "integrity": "sha512-bIZ94ob1rY87seDWsvBTBRxp8Ja2Y46DLtQYuaylHUQuK+I2xKy8XKL2ZHPsOfuwhXRqm+q+91PDjPEAdH1dQw==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float32-max": "^0.2.2", "@stdlib/constants-float32-smallest-normal": "^0.2.2", "@stdlib/constants-float64-ln-sqrt-two-pi": "^0.2.2", "@stdlib/constants-float64-pinf": "^0.2.2", "@stdlib/constants-float64-sqrt-two-pi": "^0.2.2", "@stdlib/constants-float64-two-pi": "^0.2.2", "@stdlib/math-base-assert-is-nan": "^0.2.2", "@stdlib/math-base-special-abs": "^0.2.2", "@stdlib/math-base-special-erfcinv": "^0.2.3", "@stdlib/math-base-special-exp": "^0.2.4", "@stdlib/math-base-special-gamma": "^0.3.0", "@stdlib/math-base-special-gammainc": "^0.2.2", "@stdlib/math-base-special-gammaln": "^0.2.2", "@stdlib/math-base-special-ln": "^0.2.4", "@stdlib/math-base-special-min": "^0.2.3", "@stdlib/math-base-special-pow": "^0.3.0", "@stdlib/math-base-special-sqrt": "^0.2.2", "@stdlib/math-base-tools-evalpoly": "^0.2.2", "debug": "^2.6.9"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-gammaincinv/node_modules/@stdlib/math-base-special-gamma": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-gamma/-/math-base-special-gamma-0.3.0.tgz", "integrity": "sha512-YfW+e5xuSDoUxgpquXPrFtAbdwOzE7Kqt7M0dcAkDNot8/yUn+QmrDGzURyBVzUyhRm9SaC9bACHxTShdJkcuA==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float64-eulergamma": "^0.2.2", "@stdlib/constants-float64-ninf": "^0.2.2", "@stdlib/constants-float64-pi": "^0.2.2", "@stdlib/constants-float64-pinf": "^0.2.2", "@stdlib/constants-float64-sqrt-two-pi": "^0.2.2", "@stdlib/math-base-assert-is-integer": "^0.2.5", "@stdlib/math-base-assert-is-nan": "^0.2.2", "@stdlib/math-base-assert-is-negative-zero": "^0.2.2", "@stdlib/math-base-napi-unary": "^0.2.3", "@stdlib/math-base-special-abs": "^0.2.2", "@stdlib/math-base-special-exp": "^0.2.4", "@stdlib/math-base-special-floor": "^0.2.3", "@stdlib/math-base-special-pow": "^0.3.0", "@stdlib/math-base-special-sin": "^0.2.1", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-gammaincinv/node_modules/@stdlib/math-base-special-pow": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-pow/-/math-base-special-pow-0.3.0.tgz", "integrity": "sha512-sMDYRUYGFyMXDHcCYy7hE07lV7jgI6rDspLMROKyESWcH4n8j54XE4/0w0i8OpdzR40H895MaPWU/tVnU1tP6w==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float64-exponent-bias": "^0.2.2", "@stdlib/constants-float64-high-word-abs-mask": "^0.2.2", "@stdlib/constants-float64-high-word-significand-mask": "^0.2.2", "@stdlib/constants-float64-ln-two": "^0.2.2", "@stdlib/constants-float64-ninf": "^0.2.2", "@stdlib/constants-float64-pinf": "^0.2.2", "@stdlib/math-base-assert-is-infinite": "^0.2.2", "@stdlib/math-base-assert-is-integer": "^0.2.5", "@stdlib/math-base-assert-is-nan": "^0.2.2", "@stdlib/math-base-assert-is-odd": "^0.3.0", "@stdlib/math-base-napi-binary": "^0.3.0", "@stdlib/math-base-special-abs": "^0.2.2", "@stdlib/math-base-special-copysign": "^0.2.1", "@stdlib/math-base-special-ldexp": "^0.2.2", "@stdlib/math-base-special-sqrt": "^0.2.2", "@stdlib/number-float64-base-get-high-word": "^0.2.2", "@stdlib/number-float64-base-set-high-word": "^0.2.2", "@stdlib/number-float64-base-set-low-word": "^0.2.2", "@stdlib/number-float64-base-to-words": "^0.2.2", "@stdlib/number-uint32-base-to-int32": "^0.2.2", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-gammaln": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-gammaln/-/math-base-special-gammaln-0.2.2.tgz", "integrity": "sha512-opG6HUlspi/GLvQAr4pcwyAevm7BYuymlopgNZ1VulWUvksDpytalaX3zva0idlD2HvniKrDmzHngT1N9p0J1A==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float64-pi": "^0.2.2", "@stdlib/constants-float64-pinf": "^0.2.2", "@stdlib/math-base-assert-is-infinite": "^0.2.2", "@stdlib/math-base-assert-is-nan": "^0.2.2", "@stdlib/math-base-special-abs": "^0.2.2", "@stdlib/math-base-special-ln": "^0.2.4", "@stdlib/math-base-special-sinpi": "^0.2.1", "@stdlib/math-base-special-trunc": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-gcd": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-gcd/-/math-base-special-gcd-0.2.1.tgz", "integrity": "sha512-w10k9W176lDkbiDIwnmVr1nkTyypTQLwA3/CN9qEUmXh/u8NlxkSnDYBpArcWnxE0oFaIggw8sLJ58TuMvxMaw==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float64-ninf": "^0.2.1", "@stdlib/constants-float64-pinf": "^0.2.1", "@stdlib/constants-int32-max": "^0.2.1", "@stdlib/math-base-assert-is-integer": "^0.2.1", "@stdlib/math-base-assert-is-nan": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-gcd/node_modules/@stdlib/constants-int32-max": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/@stdlib/constants-int32-max/-/constants-int32-max-0.2.1.tgz", "integrity": "sha512-vKtp3q/HdAeGG8BJBZdNzFrYpVQeleODgvOxh9Pn/TX1Ktjc50I9TVl7nTVWsT2QnacruOorILk2zNsdgBHPUQ==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-kernel-betainc": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-kernel-betainc/-/math-base-special-kernel-betainc-0.2.2.tgz", "integrity": "sha512-DQwQUWQkmZtjRgdvZ1yZOEdAYLQoEUEndbr47Z69Oe6AgwKwxxpZUh09h9imKheFCFHLVnwVUz20azIM5KifQw==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float64-e": "^0.2.2", "@stdlib/constants-float64-eps": "^0.2.2", "@stdlib/constants-float64-gamma-lanczos-g": "^0.2.2", "@stdlib/constants-float64-half-pi": "^0.2.2", "@stdlib/constants-float64-max": "^0.2.2", "@stdlib/constants-float64-max-ln": "^0.2.2", "@stdlib/constants-float64-min-ln": "^0.2.2", "@stdlib/constants-float64-pi": "^0.2.2", "@stdlib/constants-float64-smallest-normal": "^0.2.2", "@stdlib/constants-int32-max": "^0.3.0", "@stdlib/math-base-assert-is-nan": "^0.2.2", "@stdlib/math-base-special-abs": "^0.2.2", "@stdlib/math-base-special-asin": "^0.2.3", "@stdlib/math-base-special-beta": "^0.3.0", "@stdlib/math-base-special-binomcoef": "^0.2.3", "@stdlib/math-base-special-exp": "^0.2.4", "@stdlib/math-base-special-expm1": "^0.2.3", "@stdlib/math-base-special-factorial": "^0.3.0", "@stdlib/math-base-special-floor": "^0.2.3", "@stdlib/math-base-special-gamma": "^0.3.0", "@stdlib/math-base-special-gamma-delta-ratio": "^0.2.2", "@stdlib/math-base-special-gamma-lanczos-sum-expg-scaled": "^0.3.0", "@stdlib/math-base-special-gammainc": "^0.2.1", "@stdlib/math-base-special-gammaln": "^0.2.2", "@stdlib/math-base-special-ln": "^0.2.4", "@stdlib/math-base-special-log1p": "^0.2.3", "@stdlib/math-base-special-max": "^0.3.0", "@stdlib/math-base-special-maxabs": "^0.3.0", "@stdlib/math-base-special-min": "^0.2.3", "@stdlib/math-base-special-minabs": "^0.2.3", "@stdlib/math-base-special-pow": "^0.3.0", "@stdlib/math-base-special-sqrt": "^0.2.2", "@stdlib/math-base-tools-continued-fraction": "^0.2.2", "@stdlib/math-base-tools-sum-series": "^0.2.2", "@stdlib/utils-define-nonenumerable-read-only-property": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-kernel-betainc/node_modules/@stdlib/math-base-special-beta": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-beta/-/math-base-special-beta-0.3.0.tgz", "integrity": "sha512-SWUF1AZLqaEJ8g1Lj0/UOfj955AsIS3QPYH/ZMijELVxCwmp7VRgalI0AxMM09IraJt1cH5WrSwSnouH1WC3ZQ==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float64-e": "^0.2.2", "@stdlib/constants-float64-eps": "^0.2.2", "@stdlib/math-base-assert-is-nan": "^0.2.2", "@stdlib/math-base-napi-binary": "^0.3.0", "@stdlib/math-base-special-abs": "^0.2.2", "@stdlib/math-base-special-exp": "^0.2.4", "@stdlib/math-base-special-log1p": "^0.2.3", "@stdlib/math-base-special-pow": "^0.3.0", "@stdlib/math-base-special-sqrt": "^0.2.2", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-kernel-betainc/node_modules/@stdlib/math-base-special-factorial": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-factorial/-/math-base-special-factorial-0.3.0.tgz", "integrity": "sha512-tXdXqstF4gmy4HpzALo3Bhkj2UQSlyk+PU3alWXZH5XtKUozHuXhQDnak+2c4w0JqnKxHq4mnaR2qgjfkDNZcA==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float64-max-safe-nth-factorial": "^0.1.0", "@stdlib/constants-float64-pinf": "^0.2.2", "@stdlib/math-base-assert-is-integer": "^0.2.5", "@stdlib/math-base-assert-is-nan": "^0.2.2", "@stdlib/math-base-napi-unary": "^0.2.3", "@stdlib/math-base-special-gamma": "^0.2.1", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-kernel-betainc/node_modules/@stdlib/math-base-special-factorial/node_modules/@stdlib/math-base-assert-is-odd": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/@stdlib/math-base-assert-is-odd/-/math-base-assert-is-odd-0.2.1.tgz", "integrity": "sha512-V4qQuCO6/AA5udqlNatMRZ8R/MgpqD8mPIkFrpSZJdpLcGYSz815uAAf3NBOuWXkE2Izw0/Tg/hTQ+YcOW2g5g==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/math-base-assert-is-even": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-kernel-betainc/node_modules/@stdlib/math-base-special-factorial/node_modules/@stdlib/math-base-special-gamma": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-gamma/-/math-base-special-gamma-0.2.1.tgz", "integrity": "sha512-Sfq1HnVoL4kN9EDHH3YparEAF0r7QD5jNFppUTOXmrqkofgImSl5tLttttnr2I7O9zsNhYkBAiTx9q0y25bAiA==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float64-eulergamma": "^0.2.1", "@stdlib/constants-float64-ninf": "^0.2.1", "@stdlib/constants-float64-pi": "^0.2.1", "@stdlib/constants-float64-pinf": "^0.2.1", "@stdlib/constants-float64-sqrt-two-pi": "^0.2.1", "@stdlib/math-base-assert-is-integer": "^0.2.1", "@stdlib/math-base-assert-is-nan": "^0.2.1", "@stdlib/math-base-assert-is-negative-zero": "^0.2.1", "@stdlib/math-base-special-abs": "^0.2.1", "@stdlib/math-base-special-exp": "^0.2.1", "@stdlib/math-base-special-floor": "^0.2.1", "@stdlib/math-base-special-pow": "^0.2.1", "@stdlib/math-base-special-sin": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-kernel-betainc/node_modules/@stdlib/math-base-special-factorial/node_modules/@stdlib/math-base-special-pow": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-pow/-/math-base-special-pow-0.2.1.tgz", "integrity": "sha512-7SvgVzDkuilZKrHh4tPiXx9fypF/V7PSvAcUVjvcRj5kVEwv/15RpzlmCJlT9B20VPSx4gJ1S0UIA6xgmYFuAg==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float64-exponent-bias": "^0.2.1", "@stdlib/constants-float64-high-word-abs-mask": "^0.2.1", "@stdlib/constants-float64-high-word-significand-mask": "^0.2.1", "@stdlib/constants-float64-ln-two": "^0.2.1", "@stdlib/constants-float64-ninf": "^0.2.1", "@stdlib/constants-float64-pinf": "^0.2.1", "@stdlib/math-base-assert-is-infinite": "^0.2.1", "@stdlib/math-base-assert-is-integer": "^0.2.1", "@stdlib/math-base-assert-is-nan": "^0.2.1", "@stdlib/math-base-assert-is-odd": "^0.2.1", "@stdlib/math-base-special-abs": "^0.2.1", "@stdlib/math-base-special-copysign": "^0.2.1", "@stdlib/math-base-special-ldexp": "^0.2.1", "@stdlib/math-base-special-sqrt": "^0.2.1", "@stdlib/number-float64-base-get-high-word": "^0.2.1", "@stdlib/number-float64-base-set-high-word": "^0.2.1", "@stdlib/number-float64-base-set-low-word": "^0.2.1", "@stdlib/number-float64-base-to-words": "^0.2.1", "@stdlib/number-uint32-base-to-int32": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-kernel-betainc/node_modules/@stdlib/math-base-special-gamma": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-gamma/-/math-base-special-gamma-0.3.0.tgz", "integrity": "sha512-YfW+e5xuSDoUxgpquXPrFtAbdwOzE7Kqt7M0dcAkDNot8/yUn+QmrDGzURyBVzUyhRm9SaC9bACHxTShdJkcuA==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float64-eulergamma": "^0.2.2", "@stdlib/constants-float64-ninf": "^0.2.2", "@stdlib/constants-float64-pi": "^0.2.2", "@stdlib/constants-float64-pinf": "^0.2.2", "@stdlib/constants-float64-sqrt-two-pi": "^0.2.2", "@stdlib/math-base-assert-is-integer": "^0.2.5", "@stdlib/math-base-assert-is-nan": "^0.2.2", "@stdlib/math-base-assert-is-negative-zero": "^0.2.2", "@stdlib/math-base-napi-unary": "^0.2.3", "@stdlib/math-base-special-abs": "^0.2.2", "@stdlib/math-base-special-exp": "^0.2.4", "@stdlib/math-base-special-floor": "^0.2.3", "@stdlib/math-base-special-pow": "^0.3.0", "@stdlib/math-base-special-sin": "^0.2.1", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-kernel-betainc/node_modules/@stdlib/math-base-special-max": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-max/-/math-base-special-max-0.3.0.tgz", "integrity": "sha512-yXsmdFCLHRB24l34Kn1kHZXHKoGqBxPY/5Mi+n5qLg+FwrX85ZG6KGVbO3DfcpG1NxDTcEKb1hxbUargI0P5fw==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float64-pinf": "^0.2.2", "@stdlib/math-base-assert-is-nan": "^0.2.2", "@stdlib/math-base-assert-is-positive-zero": "^0.2.2", "@stdlib/math-base-napi-binary": "^0.2.1", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-kernel-betainc/node_modules/@stdlib/math-base-special-max/node_modules/@stdlib/math-base-napi-binary": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/@stdlib/math-base-napi-binary/-/math-base-napi-binary-0.2.1.tgz", "integrity": "sha512-ewGarSRaz5gaLsE17yJ4me03e56ICgPAA0ru0SYFCeMK2E5Z4Z2Lbu7HAQTTg+8XhpoaZSw0h2GJopTV7PCKmw==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/complex-float32": "^0.2.1", "@stdlib/complex-float64": "^0.2.1", "@stdlib/complex-reim": "^0.2.1", "@stdlib/complex-reimf": "^0.2.1", "@stdlib/utils-library-manifest": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-kernel-betainc/node_modules/@stdlib/math-base-special-pow": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-pow/-/math-base-special-pow-0.3.0.tgz", "integrity": "sha512-sMDYRUYGFyMXDHcCYy7hE07lV7jgI6rDspLMROKyESWcH4n8j54XE4/0w0i8OpdzR40H895MaPWU/tVnU1tP6w==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float64-exponent-bias": "^0.2.2", "@stdlib/constants-float64-high-word-abs-mask": "^0.2.2", "@stdlib/constants-float64-high-word-significand-mask": "^0.2.2", "@stdlib/constants-float64-ln-two": "^0.2.2", "@stdlib/constants-float64-ninf": "^0.2.2", "@stdlib/constants-float64-pinf": "^0.2.2", "@stdlib/math-base-assert-is-infinite": "^0.2.2", "@stdlib/math-base-assert-is-integer": "^0.2.5", "@stdlib/math-base-assert-is-nan": "^0.2.2", "@stdlib/math-base-assert-is-odd": "^0.3.0", "@stdlib/math-base-napi-binary": "^0.3.0", "@stdlib/math-base-special-abs": "^0.2.2", "@stdlib/math-base-special-copysign": "^0.2.1", "@stdlib/math-base-special-ldexp": "^0.2.2", "@stdlib/math-base-special-sqrt": "^0.2.2", "@stdlib/number-float64-base-get-high-word": "^0.2.2", "@stdlib/number-float64-base-set-high-word": "^0.2.2", "@stdlib/number-float64-base-set-low-word": "^0.2.2", "@stdlib/number-float64-base-to-words": "^0.2.2", "@stdlib/number-uint32-base-to-int32": "^0.2.2", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-kernel-betaincinv": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-kernel-betaincinv/-/math-base-special-kernel-betaincinv-0.1.1.tgz", "integrity": "sha512-DZLALmQj0m3Wx8L8/na8Jj9vluNj4Z5DxmAPvnA1AWGYy7KsotmP4HXwgSTlsfbXeF3iGcrmworPOo4HJUSxIQ==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float64-eps": "^0.2.1", "@stdlib/constants-float64-half-pi": "^0.2.1", "@stdlib/constants-float64-max": "^0.2.1", "@stdlib/constants-float64-pi": "^0.2.1", "@stdlib/constants-float64-smallest-normal": "^0.2.1", "@stdlib/constants-float64-smallest-subnormal": "^0.2.1", "@stdlib/constants-float64-sqrt-two": "^0.2.1", "@stdlib/math-base-special-abs": "^0.2.1", "@stdlib/math-base-special-acos": "^0.2.1", "@stdlib/math-base-special-asin": "^0.2.1", "@stdlib/math-base-special-beta": "^0.2.1", "@stdlib/math-base-special-betainc": "^0.2.1", "@stdlib/math-base-special-cos": "^0.2.1", "@stdlib/math-base-special-erfcinv": "^0.2.1", "@stdlib/math-base-special-exp": "^0.2.1", "@stdlib/math-base-special-expm1": "^0.2.1", "@stdlib/math-base-special-floor": "^0.2.1", "@stdlib/math-base-special-gamma-delta-ratio": "^0.2.1", "@stdlib/math-base-special-gammaincinv": "^0.2.1", "@stdlib/math-base-special-kernel-betainc": "^0.2.1", "@stdlib/math-base-special-ldexp": "^0.2.1", "@stdlib/math-base-special-ln": "^0.2.1", "@stdlib/math-base-special-log1p": "^0.2.1", "@stdlib/math-base-special-max": "^0.2.1", "@stdlib/math-base-special-min": "^0.2.1", "@stdlib/math-base-special-pow": "^0.2.1", "@stdlib/math-base-special-round": "^0.2.1", "@stdlib/math-base-special-signum": "^0.2.1", "@stdlib/math-base-special-sin": "^0.2.1", "@stdlib/math-base-special-sqrt": "^0.2.1", "@stdlib/math-base-tools-evalpoly": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-kernel-cos": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-kernel-cos/-/math-base-special-kernel-cos-0.2.3.tgz", "integrity": "sha512-K5FbN25SmEc5Z89GejUkrZpqCv05ZX6D7g9SUFcKWFJ1fwiZNgxrF8q4aJtGDQhuV3q66C1gaKJyQeLq/OI8lQ==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/math-base-napi-binary": "^0.3.0", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-kernel-sin": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-kernel-sin/-/math-base-special-kernel-sin-0.2.3.tgz", "integrity": "sha512-PFnlGdapUaCaMXqZr+tG5Ioq+l4TCyGE5e8XEYlsyhNDILf0XE2ghHzlROA/wW365Arl4sPLWUoo4oH98DUPqw==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/math-base-napi-binary": "^0.3.0", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-ldexp": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-ldexp/-/math-base-special-ldexp-0.2.3.tgz", "integrity": "sha512-yD4YisQGVTJmTJUshuzpaoq34sxJtrU+Aw4Ih39mzgXiQi6sh3E3nijB8WXDNKr2v05acUWJ1PRMkkJSfu16Kg==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float64-exponent-bias": "^0.2.2", "@stdlib/constants-float64-max-base2-exponent": "^0.2.2", "@stdlib/constants-float64-max-base2-exponent-subnormal": "^0.2.1", "@stdlib/constants-float64-min-base2-exponent-subnormal": "^0.2.1", "@stdlib/constants-float64-ninf": "^0.2.2", "@stdlib/constants-float64-pinf": "^0.2.2", "@stdlib/math-base-assert-is-infinite": "^0.2.2", "@stdlib/math-base-assert-is-nan": "^0.2.2", "@stdlib/math-base-special-abs": "^0.2.2", "@stdlib/math-base-special-copysign": "^0.2.1", "@stdlib/number-float32-base-to-word": "^0.2.2", "@stdlib/number-float64-base-exponent": "^0.2.2", "@stdlib/number-float64-base-from-words": "^0.2.2", "@stdlib/number-float64-base-normalize": "^0.2.3", "@stdlib/number-float64-base-to-words": "^0.2.2", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-ln": {"version": "0.2.4", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-ln/-/math-base-special-ln-0.2.4.tgz", "integrity": "sha512-lSB47USaixrEmxwadT0/yByvTtxNhaRwN0FIXt5oj38bsgMXGW4V8xrANOy1N+hrn3KGfHJNDyFPYbXWVdMTIw==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float64-exponent-bias": "^0.2.2", "@stdlib/constants-float64-ninf": "^0.2.2", "@stdlib/math-base-assert-is-nan": "^0.2.2", "@stdlib/math-base-napi-unary": "^0.2.1", "@stdlib/number-float64-base-get-high-word": "^0.2.2", "@stdlib/number-float64-base-set-high-word": "^0.2.2", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-log1p": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-log1p/-/math-base-special-log1p-0.2.3.tgz", "integrity": "sha512-1Pu3attNR+DcskIvhvyls+2KRZ0UCHQ/jP2tvgFI9bWDCgb4oEimXPzjFteGNg9Mj6WlAW2b9wU9tHt3bp8R3g==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float64-exponent-bias": "^0.2.2", "@stdlib/constants-float64-ninf": "^0.2.2", "@stdlib/constants-float64-pinf": "^0.2.2", "@stdlib/math-base-assert-is-nan": "^0.2.2", "@stdlib/math-base-napi-unary": "^0.2.1", "@stdlib/number-float64-base-get-high-word": "^0.2.2", "@stdlib/number-float64-base-set-high-word": "^0.2.2", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-log1pmx": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-log1pmx/-/math-base-special-log1pmx-0.2.3.tgz", "integrity": "sha512-HfjDXcbFztm/GQRrn7a9FMYS0rm/4VPXWa50sYQzBHSYaEwYv5Y1awaZz+cA/ncuqAq1Mw0dfcwEMNRmZtnxEQ==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float64-eps": "^0.2.2", "@stdlib/math-base-napi-unary": "^0.2.3", "@stdlib/math-base-special-abs": "^0.2.2", "@stdlib/math-base-special-ln": "^0.2.4", "@stdlib/math-base-special-log1p": "^0.2.3", "@stdlib/math-base-tools-sum-series": "^0.2.2", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-max": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-max/-/math-base-special-max-0.2.1.tgz", "integrity": "sha512-jsA3x5azfclbULDFwvHjNlB2nciUDHwrw7qHP/QlSdJi47E1iBDNYdzhlOa3JKzblbrITpzgZEsGBcpCinEInQ==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float64-pinf": "^0.2.1", "@stdlib/math-base-assert-is-nan": "^0.2.1", "@stdlib/math-base-assert-is-positive-zero": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-maxabs": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-maxabs/-/math-base-special-maxabs-0.3.0.tgz", "integrity": "sha512-SDj+rGD9itZ/YG2hKzhLX4Tf13SNJdOyNsMy1ezjec6Az3xJXKzv2wJAJIteo0KF6jQnEDkI/F6OIF65MY+o0g==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/math-base-napi-binary": "^0.2.1", "@stdlib/math-base-special-abs": "^0.2.2", "@stdlib/math-base-special-max": "^0.2.1", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-maxabs/node_modules/@stdlib/math-base-napi-binary": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/@stdlib/math-base-napi-binary/-/math-base-napi-binary-0.2.1.tgz", "integrity": "sha512-ewGarSRaz5gaLsE17yJ4me03e56ICgPAA0ru0SYFCeMK2E5Z4Z2Lbu7HAQTTg+8XhpoaZSw0h2GJopTV7PCKmw==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/complex-float32": "^0.2.1", "@stdlib/complex-float64": "^0.2.1", "@stdlib/complex-reim": "^0.2.1", "@stdlib/complex-reimf": "^0.2.1", "@stdlib/utils-library-manifest": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-min": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-min/-/math-base-special-min-0.2.3.tgz", "integrity": "sha512-tNrKnkcHCRVWzteZJpZ/xql9B6N6EzecnUVizDYqG9y66bOVtI+TADcQ5I/bijEwAIi2BjrIVeq/TBEgQEQBkw==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float64-ninf": "^0.2.2", "@stdlib/math-base-assert-is-nan": "^0.2.2", "@stdlib/math-base-assert-is-negative-zero": "^0.2.2", "@stdlib/math-base-napi-binary": "^0.2.1", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-min/node_modules/@stdlib/math-base-napi-binary": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/@stdlib/math-base-napi-binary/-/math-base-napi-binary-0.2.1.tgz", "integrity": "sha512-ewGarSRaz5gaLsE17yJ4me03e56ICgPAA0ru0SYFCeMK2E5Z4Z2Lbu7HAQTTg+8XhpoaZSw0h2GJopTV7PCKmw==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/complex-float32": "^0.2.1", "@stdlib/complex-float64": "^0.2.1", "@stdlib/complex-reim": "^0.2.1", "@stdlib/complex-reimf": "^0.2.1", "@stdlib/utils-library-manifest": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-minabs": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-minabs/-/math-base-special-minabs-0.2.3.tgz", "integrity": "sha512-IV7PSL09S2GHmsxxtFgebPEwLm/wHnC1e1ulP8Uiuo2zinOiv4NXy2tpf9T+nq95d0ICFMnr9IGxFs6Nd74hRw==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/math-base-napi-binary": "^0.2.1", "@stdlib/math-base-special-abs": "^0.2.2", "@stdlib/math-base-special-min": "^0.2.2", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-minabs/node_modules/@stdlib/math-base-napi-binary": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/@stdlib/math-base-napi-binary/-/math-base-napi-binary-0.2.1.tgz", "integrity": "sha512-ewGarSRaz5gaLsE17yJ4me03e56ICgPAA0ru0SYFCeMK2E5Z4Z2Lbu7HAQTTg+8XhpoaZSw0h2GJopTV7PCKmw==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/complex-float32": "^0.2.1", "@stdlib/complex-float64": "^0.2.1", "@stdlib/complex-reim": "^0.2.1", "@stdlib/complex-reimf": "^0.2.1", "@stdlib/utils-library-manifest": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-pow": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-pow/-/math-base-special-pow-0.2.1.tgz", "integrity": "sha512-7SvgVzDkuilZKrHh4tPiXx9fypF/V7PSvAcUVjvcRj5kVEwv/15RpzlmCJlT9B20VPSx4gJ1S0UIA6xgmYFuAg==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float64-exponent-bias": "^0.2.1", "@stdlib/constants-float64-high-word-abs-mask": "^0.2.1", "@stdlib/constants-float64-high-word-significand-mask": "^0.2.1", "@stdlib/constants-float64-ln-two": "^0.2.1", "@stdlib/constants-float64-ninf": "^0.2.1", "@stdlib/constants-float64-pinf": "^0.2.1", "@stdlib/math-base-assert-is-infinite": "^0.2.1", "@stdlib/math-base-assert-is-integer": "^0.2.1", "@stdlib/math-base-assert-is-nan": "^0.2.1", "@stdlib/math-base-assert-is-odd": "^0.2.1", "@stdlib/math-base-special-abs": "^0.2.1", "@stdlib/math-base-special-copysign": "^0.2.1", "@stdlib/math-base-special-ldexp": "^0.2.1", "@stdlib/math-base-special-sqrt": "^0.2.1", "@stdlib/number-float64-base-get-high-word": "^0.2.1", "@stdlib/number-float64-base-set-high-word": "^0.2.1", "@stdlib/number-float64-base-set-low-word": "^0.2.1", "@stdlib/number-float64-base-to-words": "^0.2.1", "@stdlib/number-uint32-base-to-int32": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-pow/node_modules/@stdlib/math-base-assert-is-odd": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/@stdlib/math-base-assert-is-odd/-/math-base-assert-is-odd-0.2.1.tgz", "integrity": "sha512-V4qQuCO6/AA5udqlNatMRZ8R/MgpqD8mPIkFrpSZJdpLcGYSz815uAAf3NBOuWXkE2Izw0/Tg/hTQ+YcOW2g5g==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/math-base-assert-is-even": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-powm1": {"version": "0.3.1", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-powm1/-/math-base-special-powm1-0.3.1.tgz", "integrity": "sha512-Pz7e2JlZH9EktJCDuyFPoT9IxMUSiZiJquyh2xB92NQQi9CAIdyaPUryNo36LxG65bne5GZF47MeiWCE8oWgiA==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/math-base-assert-is-infinite": "^0.2.2", "@stdlib/math-base-assert-is-nan": "^0.2.2", "@stdlib/math-base-napi-binary": "^0.3.0", "@stdlib/math-base-special-abs": "^0.2.2", "@stdlib/math-base-special-expm1": "^0.2.3", "@stdlib/math-base-special-fmod": "^0.1.0", "@stdlib/math-base-special-ln": "^0.2.4", "@stdlib/math-base-special-pow": "^0.3.0", "@stdlib/math-base-special-trunc": "^0.2.2", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-powm1/node_modules/@stdlib/math-base-special-pow": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-pow/-/math-base-special-pow-0.3.0.tgz", "integrity": "sha512-sMDYRUYGFyMXDHcCYy7hE07lV7jgI6rDspLMROKyESWcH4n8j54XE4/0w0i8OpdzR40H895MaPWU/tVnU1tP6w==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float64-exponent-bias": "^0.2.2", "@stdlib/constants-float64-high-word-abs-mask": "^0.2.2", "@stdlib/constants-float64-high-word-significand-mask": "^0.2.2", "@stdlib/constants-float64-ln-two": "^0.2.2", "@stdlib/constants-float64-ninf": "^0.2.2", "@stdlib/constants-float64-pinf": "^0.2.2", "@stdlib/math-base-assert-is-infinite": "^0.2.2", "@stdlib/math-base-assert-is-integer": "^0.2.5", "@stdlib/math-base-assert-is-nan": "^0.2.2", "@stdlib/math-base-assert-is-odd": "^0.3.0", "@stdlib/math-base-napi-binary": "^0.3.0", "@stdlib/math-base-special-abs": "^0.2.2", "@stdlib/math-base-special-copysign": "^0.2.1", "@stdlib/math-base-special-ldexp": "^0.2.2", "@stdlib/math-base-special-sqrt": "^0.2.2", "@stdlib/number-float64-base-get-high-word": "^0.2.2", "@stdlib/number-float64-base-set-high-word": "^0.2.2", "@stdlib/number-float64-base-set-low-word": "^0.2.2", "@stdlib/number-float64-base-to-words": "^0.2.2", "@stdlib/number-uint32-base-to-int32": "^0.2.2", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-rempio2": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-rempio2/-/math-base-special-rempio2-0.2.1.tgz", "integrity": "sha512-ErV5EAe3SQCSijg4Pi4Z0sRPOGrODF3jkyCeiLM+iYj2TMOwDaOWQ0xCTME0p9G45TDrbZCLM5arxN83TfzgXQ==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/array-base-zeros": "^0.2.1", "@stdlib/constants-float64-high-word-abs-mask": "^0.2.1", "@stdlib/constants-float64-high-word-exponent-mask": "^0.2.1", "@stdlib/constants-float64-high-word-significand-mask": "^0.2.1", "@stdlib/math-base-special-floor": "^0.2.1", "@stdlib/math-base-special-ldexp": "^0.2.1", "@stdlib/math-base-special-round": "^0.2.1", "@stdlib/number-float64-base-from-words": "^0.2.1", "@stdlib/number-float64-base-get-high-word": "^0.2.1", "@stdlib/number-float64-base-get-low-word": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-round": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-round/-/math-base-special-round-0.2.1.tgz", "integrity": "sha512-ibeKiN9z//6wS4H4uaa+vGnh/t1vJtZYXz+NqRVtwoP+nnE/mtL+fIrBlAnkIWVIH+smQPNNo8qsohjyGLBvUQ==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-signum": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-signum/-/math-base-special-signum-0.2.2.tgz", "integrity": "sha512-cszwgkfeMTnUiORRWdWv6Q/tpoXkXkMYNMoAFO5qzHTuahnDP37Lkn8fTmCEtgHEasg3Cm69xLbqP0UDuNPHyA==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/math-base-assert-is-nan": "^0.2.2", "@stdlib/math-base-napi-unary": "^0.2.1", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-sin": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-sin/-/math-base-special-sin-0.2.1.tgz", "integrity": "sha512-IQ6+bzfiZ6/VUn5DIe6iwCsYERE1pwtAOsAWkgNZ1Ih3FzXUxdEOyHtv1zraPrVUb8mR+V5q7OfAGy8TCTnkUg==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float64-high-word-abs-mask": "^0.2.1", "@stdlib/constants-float64-high-word-exponent-mask": "^0.2.1", "@stdlib/math-base-special-kernel-cos": "^0.2.1", "@stdlib/math-base-special-kernel-sin": "^0.2.1", "@stdlib/math-base-special-rempio2": "^0.2.1", "@stdlib/number-float64-base-get-high-word": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-sinpi": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-sinpi/-/math-base-special-sinpi-0.2.1.tgz", "integrity": "sha512-Q3yCp1CoD7gemIILO28bU7iBn8OFiCgXm9vP/9q0tRBxmjtiUnjqbFd+3jRXdAmiCc/B/bPjwGBtVnCnrEMY9g==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float64-pi": "^0.2.1", "@stdlib/math-base-assert-is-infinite": "^0.2.1", "@stdlib/math-base-assert-is-nan": "^0.2.1", "@stdlib/math-base-special-abs": "^0.2.1", "@stdlib/math-base-special-copysign": "^0.2.1", "@stdlib/math-base-special-cos": "^0.2.1", "@stdlib/math-base-special-sin": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-sqrt": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-sqrt/-/math-base-special-sqrt-0.2.2.tgz", "integrity": "sha512-YWxe9vVE5blDbRPDAdZfU03vfGTBHy/8pLDa/qLz7SiJj5n5sVWKObdbMR2oPHF4c6DaZh4IYkrcHFleiY8YkQ==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/math-base-napi-unary": "^0.2.1", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-special-trunc": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/math-base-special-trunc/-/math-base-special-trunc-0.2.2.tgz", "integrity": "sha512-cvizbo6oFEbdiv7BrtEMODGW+cJcBgyAIleJnIpCf75C722Y/IZgWikWhACSjv4stxGywFubx85B7uvm3vLgwA==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/math-base-napi-unary": "^0.2.3", "@stdlib/math-base-special-ceil": "^0.2.1", "@stdlib/math-base-special-floor": "^0.2.3", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-tools-continued-fraction": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/math-base-tools-continued-fraction/-/math-base-tools-continued-fraction-0.2.2.tgz", "integrity": "sha512-5dm72lTXwSVOsBsOLF57RZqqHehRd9X3HKdQ3WhOoHx7fNc0lxJJEDjtK8gMdV3NvfoER1MBiGbs2h23oaK5qw==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/assert-has-generator-support": "^0.2.2", "@stdlib/constants-float32-smallest-normal": "^0.2.2", "@stdlib/constants-float64-eps": "^0.2.2", "@stdlib/math-base-special-abs": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-tools-evalpoly": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/math-base-tools-evalpoly/-/math-base-tools-evalpoly-0.2.2.tgz", "integrity": "sha512-vLvfkMkccXZGFiyI3GPf8Ayi6vPEZeHgENnoBDGC+eMIDIoVWmOpVWsjpUz8xtc5xGNsa1hKalSI40IrouHsYA==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/function-ctor": "^0.2.1", "@stdlib/utils-define-nonenumerable-read-only-property": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/math-base-tools-sum-series": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/math-base-tools-sum-series/-/math-base-tools-sum-series-0.2.2.tgz", "integrity": "sha512-P3X+jMONClp93ucJi1Up/x26uwL0kH20CMV9bLzcQyRY8Mceh7jPZuEwzGQR0jq/tJ/4J7AnHg4kdrx4Pd+BNA==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/assert-has-generator-support": "^0.2.2", "@stdlib/constants-float64-eps": "^0.2.2", "@stdlib/math-base-special-abs": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/number-ctor": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/number-ctor/-/number-ctor-0.2.2.tgz", "integrity": "sha512-98pL4f1uiXVIw9uRV6t4xecMFUYRRTUoctsqDDV8MSRtKEYDzqkWCNz/auupJFJ135L1ejzkejh73fASsgcwKQ==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/number-float32-base-to-word": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/number-float32-base-to-word/-/number-float32-base-to-word-0.2.2.tgz", "integrity": "sha512-/I866ocLExPpAjgZnHAjeaBw3ZHg5tVPcRdJoTPEiBG2hwD/OonHdCsfB9lu6FxO6sbp7I9BR1JolCoEyrhmYg==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/array-float32": "^0.2.2", "@stdlib/array-uint32": "^0.2.2", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/number-float64-base-exponent": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/number-float64-base-exponent/-/number-float64-base-exponent-0.2.2.tgz", "integrity": "sha512-mYivBQKCuu54ulorf5A5rIhFaGPjGvmtkxhvK14q7gzRA80si83dk8buUsLpeeYsakg7yLn10RCVjBEP9/gm7Q==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float64-exponent-bias": "^0.2.2", "@stdlib/constants-float64-high-word-exponent-mask": "^0.2.2", "@stdlib/number-float64-base-get-high-word": "^0.2.1", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/number-float64-base-from-words": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/number-float64-base-from-words/-/number-float64-base-from-words-0.2.2.tgz", "integrity": "sha512-SzMDXSnIDZ8l3PDmtN9TPKTf0mUmh83kKCtj4FisKTcTbcmUmT/ovmrpMTiqdposymjHBieNvGiCz/K03NmlAA==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/array-float64": "^0.2.1", "@stdlib/array-uint32": "^0.2.2", "@stdlib/assert-is-little-endian": "^0.2.1", "@stdlib/number-float64-base-to-words": "^0.2.1", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/number-float64-base-get-high-word": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/number-float64-base-get-high-word/-/number-float64-base-get-high-word-0.2.2.tgz", "integrity": "sha512-LMNQAHdLZepKOFMRXAXLuq30GInmEdTtR0rO7Ka4F3m7KpYvw84JMyvZByMQHBu+daR6JNr2a/o9aFjmVIe51g==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/array-float64": "^0.2.1", "@stdlib/array-uint32": "^0.2.2", "@stdlib/assert-is-little-endian": "^0.2.1", "@stdlib/number-float64-base-to-words": "^0.2.1", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/number-float64-base-get-low-word": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/number-float64-base-get-low-word/-/number-float64-base-get-low-word-0.2.2.tgz", "integrity": "sha512-VZjflvoQ9//rZwwuhl7uSLUnnscdIIYmBrHofnBHRjHwdLGUzSd9PM0iagtvI82OHw5QnydBYI4hohBeAAg+aQ==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/array-float64": "^0.2.1", "@stdlib/array-uint32": "^0.2.2", "@stdlib/assert-is-little-endian": "^0.2.1", "@stdlib/number-float64-base-to-words": "^0.2.1", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/number-float64-base-normalize": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/@stdlib/number-float64-base-normalize/-/number-float64-base-normalize-0.2.3.tgz", "integrity": "sha512-HT+3fhYZOEg2JgHBWS/ysc9ZveQZV10weKbtxhLHOsvceQVp1GbThsLik62mU2/3f96S9MgiVfPfSDI3jnBoYw==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/constants-float64-smallest-normal": "^0.2.2", "@stdlib/math-base-assert-is-infinite": "^0.2.2", "@stdlib/math-base-assert-is-nan": "^0.2.2", "@stdlib/math-base-special-abs": "^0.2.1", "@stdlib/utils-define-nonenumerable-read-only-property": "^0.2.2", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/number-float64-base-set-high-word": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/number-float64-base-set-high-word/-/number-float64-base-set-high-word-0.2.2.tgz", "integrity": "sha512-bLvH15GJgX5URMaOOJAQgO8/dCJPYUQoXPZH7ecSC3XnnVMfWEf43knkjEGYCnWp4nro5hPRElbtdV4mKEjpUg==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/array-float64": "^0.2.1", "@stdlib/array-uint32": "^0.2.2", "@stdlib/assert-is-little-endian": "^0.2.1", "@stdlib/number-float64-base-to-words": "^0.2.1", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/number-float64-base-set-low-word": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/number-float64-base-set-low-word/-/number-float64-base-set-low-word-0.2.2.tgz", "integrity": "sha512-E1pGjTwacJ+Tkt5rKQNdwitKnM1iDgMlulYosNdn6CtvU0Pkq359bNhscMscxehdY3MifwuJpuGzDWD2EGUXzQ==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/array-float64": "^0.2.1", "@stdlib/array-uint32": "^0.2.2", "@stdlib/assert-is-little-endian": "^0.2.1", "@stdlib/number-float64-base-to-words": "^0.2.1", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/number-float64-base-to-float32": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/number-float64-base-to-float32/-/number-float64-base-to-float32-0.2.2.tgz", "integrity": "sha512-T5snDkVNZY6pomrSW/qLWQfZ9JHgqCFLi8jaaarfNj2o+5NMUuvvRifLUIacTm8/uC96xB0j3+wKTh1zbIV5ig==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/array-float32": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/number-float64-base-to-words": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/number-float64-base-to-words/-/number-float64-base-to-words-0.2.2.tgz", "integrity": "sha512-nkFHHXoMhb3vcfl7ZvzgiNdqBdBfbKxHtgvDXRxrNQoVmyYbnjljjYD489d2/TAhe+Zvn7qph6QLgTod3zaeww==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/array-float64": "^0.2.1", "@stdlib/array-uint32": "^0.2.2", "@stdlib/assert-is-little-endian": "^0.2.1", "@stdlib/os-byte-order": "^0.2.1", "@stdlib/os-float-word-order": "^0.2.2", "@stdlib/utils-define-nonenumerable-read-only-property": "^0.2.2", "@stdlib/utils-library-manifest": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/number-uint32-base-to-int32": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/number-uint32-base-to-int32/-/number-uint32-base-to-int32-0.2.2.tgz", "integrity": "sha512-NPADfdHE/3VEifKDttXM24dRj5YQqxwh2wTRD8fQrpHeaWiMIUo8yDqWrrFNIdLVAcqjL2SwWpo4VJ7oKTYaIA==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/object-ctor": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/@stdlib/object-ctor/-/object-ctor-0.2.1.tgz", "integrity": "sha512-HEIBBpfdQS9Nh5mmIqMk9fzedx6E0tayJrVa2FD7No86rVuq/Ikxq1QP7qNXm+i6z9iNUUS/lZq7BmJESWO/Zg==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/os-byte-order": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/os-byte-order/-/os-byte-order-0.2.2.tgz", "integrity": "sha512-2y6rHAvZo43YmZu9u/E/7cnqZa0hNTLoIiMpV1IxQ/7iv03xZ45Z3xyvWMk0b7bAWwWL7iUknOAAmEchK/kHBA==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/assert-is-big-endian": "^0.2.1", "@stdlib/assert-is-little-endian": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/os-float-word-order": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/os-float-word-order/-/os-float-word-order-0.2.2.tgz", "integrity": "sha512-5xpcEuxv/CudKctHS5czKdM7Bj/gC+sm/5R5bRPYyqxANM67t365j3v2v8rmmOxkEp9t0fa8Dggx8VmOkpJXaA==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/os-byte-order": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/process-cwd": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/process-cwd/-/process-cwd-0.2.2.tgz", "integrity": "sha512-8Q/nA/ud5d5PEzzG6ZtKzcOw+RMLm5CWR8Wd+zVO5vcPj+JD7IV7M2lBhbzfUzr63Torrf/vEhT3cob8vUHV/A==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/regexp-extended-length-path": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/regexp-extended-length-path/-/regexp-extended-length-path-0.2.2.tgz", "integrity": "sha512-z3jqauEsaxpsQU3rj1A1QnOgu17pyW5kt+Az8QkoTk7wqNE8HhPikI6k4o7XBHV689rSFWZCl4c4W+7JAiNObQ==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/utils-define-nonenumerable-read-only-property": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/regexp-function-name": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/regexp-function-name/-/regexp-function-name-0.2.2.tgz", "integrity": "sha512-0z/KRsgHJJ3UQkmBeLH+Nin0hXIeA+Fw1T+mnG2V5CHnTA6FKlpxJxWrvwLEsRX7mR/DNtDp06zGyzMFE/4kig==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/utils-define-nonenumerable-read-only-property": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/stats-base-dists-t-quantile": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/@stdlib/stats-base-dists-t-quantile/-/stats-base-dists-t-quantile-0.2.1.tgz", "integrity": "sha512-59sdJjHsOMd9JlZ/Kdz4Jc/QHESejDRATw/G/zHafMrO6vIhaus9Y5O2PYUzPQx7nR6i5hsXnT8OQwqZ+RoVNQ==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/math-base-assert-is-nan": "^0.2.1", "@stdlib/math-base-special-kernel-betaincinv": "^0.1.1", "@stdlib/math-base-special-signum": "^0.2.1", "@stdlib/math-base-special-sqrt": "^0.2.1", "@stdlib/utils-constant-function": "^0.2.1", "@stdlib/utils-define-nonenumerable-read-only-property": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/string-base-format-interpolate": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/string-base-format-interpolate/-/string-base-format-interpolate-0.2.2.tgz", "integrity": "sha512-i9nU9rAB2+o/RR66TS9iQ8x+YzeUDL1SGiAo6GY3hP6Umz5Dx9Qp/v8T69gWVsb4a1YSclz5+YeCWaFgwvPjKA==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/string-base-format-tokenize": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/string-base-format-tokenize/-/string-base-format-tokenize-0.2.2.tgz", "integrity": "sha512-kXq2015i+LJjqth5dN+hYnvJXBSzRm8w0ABWB5tYAsIuQTpQK+mSo2muM8JBEFEnqUHAwpUsu2qNTK/9o8lsJg==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/string-base-lowercase": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/@stdlib/string-base-lowercase/-/string-base-lowercase-0.4.0.tgz", "integrity": "sha512-IH35Z5e4T+S3b3SfYY39mUhrD2qvJVp4VS7Rn3+jgj4+C3syocuAPsJ8C4OQXWGfblX/N9ymizbpFBCiVvMW8w==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/string-base-replace": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/string-base-replace/-/string-base-replace-0.2.2.tgz", "integrity": "sha512-Y4jZwRV4Uertw7AlA/lwaYl1HjTefSriN5+ztRcQQyDYmoVN3gzoVKLJ123HPiggZ89vROfC+sk/6AKvly+0CA==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/string-format": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/string-format/-/string-format-0.2.2.tgz", "integrity": "sha512-GUa50uxgMAtoItsxTbMmwkyhIwrCxCrsjzk3nAbLnt/1Kt1EWOWMwsALqZdD6K4V/xSJ4ns6PZur3W6w+vKk9g==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/string-base-format-interpolate": "^0.2.1", "@stdlib/string-base-format-tokenize": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/string-replace": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/string-replace/-/string-replace-0.2.2.tgz", "integrity": "sha512-czNS5IU7sBuHjac45Y3VWUTsUoi82yc8JsMZrOMcjgSrEuDrVmA6sNJg7HC1DuSpdPjm/v9uUk102s1gIfk3Nw==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/assert-is-function": "^0.2.2", "@stdlib/assert-is-regexp": "^0.2.2", "@stdlib/assert-is-string": "^0.2.2", "@stdlib/error-tools-fmtprodmsg": "^0.2.2", "@stdlib/string-base-replace": "^0.2.2", "@stdlib/string-format": "^0.2.2", "@stdlib/utils-escape-regexp-string": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/symbol-ctor": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/symbol-ctor/-/symbol-ctor-0.2.2.tgz", "integrity": "sha512-XsmiTfHnTb9jSPf2SoK3O0wrNOXMxqzukvDvtzVur1XBKfim9+seaAS4akmV1H3+AroAXQWVtde885e1B6jz1w==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/utils-constant-function": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/utils-constant-function/-/utils-constant-function-0.2.2.tgz", "integrity": "sha512-ezRenGy5zU4R0JTfha/bpF8U+Hx0b52AZV++ca/pcaQVvPBRkgCsJacXX0eDbexoBB4+ZZ1vcyIi4RKJ0RRlbQ==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/utils-constructor-name": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/utils-constructor-name/-/utils-constructor-name-0.2.2.tgz", "integrity": "sha512-TBtO3MKDAf05ij5ajmyBCbpKKt0Lfahn5tu18gqds4PkFltgcw5tVZfSHY5DZ2HySJQ2GMMYjPW2Kbg6yPCSVg==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/assert-is-buffer": "^0.2.1", "@stdlib/regexp-function-name": "^0.2.2", "@stdlib/utils-native-class": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/utils-convert-path": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/utils-convert-path/-/utils-convert-path-0.2.2.tgz", "integrity": "sha512-8nNuAgt23Np9NssjShUrPK42c6gRTweGuoQw+yTpTfBR9VQv8WFyt048n8gRGUlAHizrdMNpEY9VAb7IBzpVYw==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/assert-is-string": "^0.2.2", "@stdlib/error-tools-fmtprodmsg": "^0.2.2", "@stdlib/regexp-extended-length-path": "^0.2.2", "@stdlib/string-base-lowercase": "^0.4.0", "@stdlib/string-format": "^0.2.2", "@stdlib/string-replace": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/utils-define-nonenumerable-read-only-property": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/utils-define-nonenumerable-read-only-property/-/utils-define-nonenumerable-read-only-property-0.2.2.tgz", "integrity": "sha512-V3mpAesJemLYDKG376CsmoczWPE/4LKsp8xBvUxCt5CLNAx3J/1W39iZQyA5q6nY1RStGinGn1/dYZwa8ig0Uw==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/utils-define-property": "^0.2.3"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/utils-define-property": {"version": "0.2.4", "resolved": "https://registry.npmjs.org/@stdlib/utils-define-property/-/utils-define-property-0.2.4.tgz", "integrity": "sha512-XlMdz7xwuw/sqXc9LbsV8XunCzZXjbZPC+OAdf4t4PBw4ZRwGzlTI6WED+f4PYR5Tp9F1cHgLPyMYCIBfA2zRg==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/error-tools-fmtprodmsg": "^0.2.1", "@stdlib/string-format": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/utils-escape-regexp-string": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/utils-escape-regexp-string/-/utils-escape-regexp-string-0.2.2.tgz", "integrity": "sha512-areCibzgpmvm6pGKBg+mXkSDJW4NxtS5jcAT7RtunGMdAYhA/I5whISMPaeJkIT2XhjjFkjKBaIs5pF6aPr4fQ==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/assert-is-string": "^0.2.1", "@stdlib/error-tools-fmtprodmsg": "^0.2.2", "@stdlib/string-format": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/utils-eval": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/utils-eval/-/utils-eval-0.2.2.tgz", "integrity": "sha512-MaFpWZh3fGcTjUeozju5faXqH8w4MRVfpO/M5pon3osTM0by8zrKiI5D9oWqNVygb9JBd+etE+4tj2L1nr5j2A==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/utils-get-prototype-of": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/utils-get-prototype-of/-/utils-get-prototype-of-0.2.2.tgz", "integrity": "sha512-eDb1BAvt7GW/jduBkfuQrUsA9p09mV8RW20g0DWPaxci6ORYg/UB0tdbAA23aZz2QUoxdYY5s/UJxlq/GHwoKQ==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/assert-is-function": "^0.2.1", "@stdlib/object-ctor": "^0.2.1", "@stdlib/utils-native-class": "^0.2.1"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/utils-global": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/utils-global/-/utils-global-0.2.2.tgz", "integrity": "sha512-A4E8VFHn+1bpfJ4PA8H2b62CMQpjv2A+H3QDEBrouLFWne0wrx0TNq8vH6VYHxx9ZRxhgWQjfHiSAxtUJobrbQ==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/assert-is-boolean": "^0.2.1", "@stdlib/error-tools-fmtprodmsg": "^0.2.2", "@stdlib/string-format": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/utils-library-manifest": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/utils-library-manifest/-/utils-library-manifest-0.2.2.tgz", "integrity": "sha512-YqzVLuBsB4wTqzdUtRArAjBJoT3x61iop2jFChXexhl6ejV3vDpHcukEEkqIOcJKut+1cG5TLJdexgHNt1C0NA==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/fs-resolve-parent-path": "^0.2.1", "@stdlib/utils-convert-path": "^0.2.1", "debug": "^2.6.9", "resolve": "^1.1.7"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/utils-native-class": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/utils-native-class/-/utils-native-class-0.2.2.tgz", "integrity": "sha512-cSn/FozbEpfR/FlJAoceQtZ8yUJFhZ8RFkbEsxW/7+H4o09yln3lBS0HSfUJISYNtpTNN/2/Fup88vmvwspvwA==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/assert-has-own-property": "^0.2.1", "@stdlib/assert-has-tostringtag-support": "^0.2.2", "@stdlib/symbol-ctor": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@stdlib/utils-type-of": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@stdlib/utils-type-of/-/utils-type-of-0.2.2.tgz", "integrity": "sha512-RLGFxPNgY9AtVVrFGdKO6Y3pOd/Ov2WA4O2/czZN/AbgYzbPdoF0KkfvHRIney6k+TtvoyYB8YqZXJ4G88f9BQ==", "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "dependencies": {"@stdlib/utils-constructor-name": "^0.2.1", "@stdlib/utils-global": "^0.2.2"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stdlib"}}, "node_modules/@tsconfig/node10": {"version": "1.0.11", "resolved": "https://registry.npmjs.org/@tsconfig/node10/-/node10-1.0.11.tgz", "integrity": "sha512-DcRjDCujK/kCk/cUe8Xz8ZSpm8mS3mNNpta+jGCA6USEDfktlNvm1+IuZ9eTcDbNk41BHwpHHeW+N1lKCz4zOw==", "dev": true}, "node_modules/@tsconfig/node12": {"version": "1.0.11", "resolved": "https://registry.npmjs.org/@tsconfig/node12/-/node12-1.0.11.tgz", "integrity": "sha512-cqefuRsh12pWyGsIoBKJA9luFu3mRxCA+ORZvA4ktLSzIuCUtWVxGIuXigEwO5/ywWFMZ2QEGKWvkZG1zDMTag==", "dev": true}, "node_modules/@tsconfig/node14": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/@tsconfig/node14/-/node14-1.0.3.tgz", "integrity": "sha512-ysT8mhdixWK6Hw3i1V2AeRqZ5WfXg1G43mqoYlM2nc6388Fq5jcXyr5mRsqViLx/GJYdoL0bfXD8nmF+Zn/Iow==", "dev": true}, "node_modules/@tsconfig/node16": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/@tsconfig/node16/-/node16-1.0.4.tgz", "integrity": "sha512-vxhUy4J8lyeyinH7Azl1pdd43GJhZH/tP2weN8TntQblOY+A0XbT8DJk1/oCPuOOyg/Ja757rG0CgHcWC8OfMA==", "dev": true}, "node_modules/@turf/boolean-clockwise": {"version": "6.5.0", "resolved": "https://registry.npmjs.org/@turf/boolean-clockwise/-/boolean-clockwise-6.5.0.tgz", "integrity": "sha512-45+C7LC5RMbRWrxh3Z0Eihsc8db1VGBO5d9BLTOAwU4jR6SgsunTfRWR16X7JUwIDYlCVEmnjcXJNi/kIU3VIw==", "dependencies": {"@turf/helpers": "^6.5.0", "@turf/invariant": "^6.5.0"}, "funding": {"url": "https://opencollective.com/turf"}}, "node_modules/@turf/clone": {"version": "6.5.0", "resolved": "https://registry.npmjs.org/@turf/clone/-/clone-6.5.0.tgz", "integrity": "sha512-mzVtTFj/QycXOn6ig+annKrM6ZlimreKYz6f/GSERytOpgzodbQyOgkfwru100O1KQhhjSudKK4DsQ0oyi9cTw==", "dependencies": {"@turf/helpers": "^6.5.0"}, "funding": {"url": "https://opencollective.com/turf"}}, "node_modules/@turf/flatten": {"version": "6.5.0", "resolved": "https://registry.npmjs.org/@turf/flatten/-/flatten-6.5.0.tgz", "integrity": "sha512-IBZVwoNLVNT6U/bcUUllubgElzpMsNoCw8tLqBw6dfYg9ObGmpEjf9BIYLr7a2Yn5ZR4l7YIj2T7kD5uJjZADQ==", "dependencies": {"@turf/helpers": "^6.5.0", "@turf/meta": "^6.5.0"}, "funding": {"url": "https://opencollective.com/turf"}}, "node_modules/@turf/helpers": {"version": "6.5.0", "resolved": "https://registry.npmjs.org/@turf/helpers/-/helpers-6.5.0.tgz", "integrity": "sha512-VbI1dV5bLFzohYYdgqwikdMVpe7pJ9X3E+dlr425wa2/sMJqYDhTO++ec38/pcPvPE6oD9WEEeU3Xu3gza+VPw==", "funding": {"url": "https://opencollective.com/turf"}}, "node_modules/@turf/invariant": {"version": "6.5.0", "resolved": "https://registry.npmjs.org/@turf/invariant/-/invariant-6.5.0.tgz", "integrity": "sha512-Wv8PRNCtPD31UVbdJE/KVAWKe7l6US+lJItRR/HOEW3eh+U/JwRCSUl/KZ7bmjM/C+zLNoreM2TU6OoLACs4eg==", "dependencies": {"@turf/helpers": "^6.5.0"}, "funding": {"url": "https://opencollective.com/turf"}}, "node_modules/@turf/meta": {"version": "6.5.0", "resolved": "https://registry.npmjs.org/@turf/meta/-/meta-6.5.0.tgz", "integrity": "sha512-RrArvtsV0vdsCBegoBtOalgdSOfkBrTJ07VkpiCnq/491W67hnMWmDu7e6Ztw0C3WldRYTXkg3SumfdzZxLBHA==", "dependencies": {"@turf/helpers": "^6.5.0"}, "funding": {"url": "https://opencollective.com/turf"}}, "node_modules/@turf/rewind": {"version": "6.5.0", "resolved": "https://registry.npmjs.org/@turf/rewind/-/rewind-6.5.0.tgz", "integrity": "sha512-IoUAMcHWotBWYwSYuYypw/LlqZmO+wcBpn8ysrBNbazkFNkLf3btSDZMkKJO/bvOzl55imr/Xj4fi3DdsLsbzQ==", "dependencies": {"@turf/boolean-clockwise": "^6.5.0", "@turf/clone": "^6.5.0", "@turf/helpers": "^6.5.0", "@turf/invariant": "^6.5.0", "@turf/meta": "^6.5.0"}, "funding": {"url": "https://opencollective.com/turf"}}, "node_modules/@types/node": {"version": "22.13.17", "resolved": "https://registry.npmjs.org/@types/node/-/node-22.13.17.tgz", "integrity": "sha512-nAJuQXoyPj04uLgu+obZcSmsfOenUg6DxPKogeUy6yNCFwWaj5sBF8/G/pNo8EtBJjAfSVgfIlugR/BCOleO+g==", "dev": true, "dependencies": {"undici-types": "~6.20.0"}}, "node_modules/@visactor/calculator": {"version": "2.0.5", "resolved": "https://bnpm.byted.org/@visactor/calculator/-/calculator-2.0.5.tgz", "integrity": "sha512-/NBDB/wBQLeQuSspDBuiEAbbyfJS/xPX6mubVsLGhfy65UwUBojAQgmX25FcRJnUsRXooK5heshni19DBBf8xA==", "dependencies": {"@visactor/vutils": "~0.19.3", "node-sql-parser": "~4.17.0", "ts-pattern": "~4.1.4"}}, "node_modules/@visactor/chart-advisor": {"version": "2.0.5", "resolved": "https://bnpm.byted.org/@visactor/chart-advisor/-/chart-advisor-2.0.5.tgz", "integrity": "sha512-pvHceRlworB7kDSmbWXUtherLLXh5nMj0aEGuxtzKQyHmeO0sjuu9gGXBFIgscGliSZM4tmeNrFU9eBLGJ8dxw==", "dependencies": {"@visactor/vutils": "~0.19.3"}}, "node_modules/@visactor/vchart": {"version": "1.13.8", "resolved": "https://registry.npmjs.org/@visactor/vchart/-/vchart-1.13.8.tgz", "integrity": "sha512-g8GacKxDvxUiuT4kW83u5vrAoAvpJ0+yca4IUYvdSTxdYXzipJEgiNmSnwd46GP8eBKJj36ZmqzEhLVzPJ+/Pw==", "dependencies": {"@visactor/vdataset": "~0.19.4", "@visactor/vgrammar-core": "0.16.3", "@visactor/vgrammar-hierarchy": "0.16.3", "@visactor/vgrammar-projection": "0.16.3", "@visactor/vgrammar-sankey": "0.16.3", "@visactor/vgrammar-util": "0.16.3", "@visactor/vgrammar-venn": "0.16.3", "@visactor/vgrammar-wordcloud": "0.16.3", "@visactor/vgrammar-wordcloud-shape": "0.16.3", "@visactor/vrender-components": "0.22.6", "@visactor/vrender-core": "0.22.6", "@visactor/vrender-kits": "0.22.6", "@visactor/vscale": "~0.19.4", "@visactor/vutils": "~0.19.4", "@visactor/vutils-extension": "1.13.8"}}, "node_modules/@visactor/vchart-theme": {"version": "1.12.2", "resolved": "https://registry.npmjs.org/@visactor/vchart-theme/-/vchart-theme-1.12.2.tgz", "integrity": "sha512-r298TUdK+CKbHGVYWgQnNSEB5uqpFvF2/aMNZ/2POQnd2CovAPJOx2nTE6hAcOn8rra2FwJ2xF8AyP1O5OhrTw==", "peerDependencies": {"@visactor/vchart": ">=1.10.4"}}, "node_modules/@visactor/vdataset": {"version": "0.19.4", "resolved": "https://registry.npmjs.org/@visactor/vdataset/-/vdataset-0.19.4.tgz", "integrity": "sha512-xxglcFtvho5jWiQPKwTolKXbNOG8f77CrK7TJhfiqNlzoe27qO8B+A6lUKlLMt1kZaCH7ZNrFFkHyPjnnZ/gng==", "dependencies": {"@turf/flatten": "^6.5.0", "@turf/helpers": "^6.5.0", "@turf/rewind": "^6.5.0", "@visactor/vutils": "0.19.4", "d3-dsv": "^2.0.0", "d3-geo": "^1.12.1", "d3-hexbin": "^0.2.2", "d3-hierarchy": "^3.1.1", "eventemitter3": "^4.0.7", "geobuf": "^3.0.1", "geojson-dissolve": "^3.1.0", "path-browserify": "^1.0.1", "pbf": "^3.2.1", "point-at-length": "^1.1.0", "simple-statistics": "^7.7.3", "simplify-geojson": "^1.0.4", "topojson-client": "^3.1.0"}}, "node_modules/@visactor/vgrammar-coordinate": {"version": "0.16.3", "resolved": "https://registry.npmjs.org/@visactor/vgrammar-coordinate/-/vgrammar-coordinate-0.16.3.tgz", "integrity": "sha512-tfDSi3WgY/GWDvbf67eus4a7jR74y7OMod3JrTqyDVzSNZUOgUtS3ieEM71f9yipxjY8gxo53GPDpH/advxUZw==", "dependencies": {"@visactor/vgrammar-util": "0.16.3", "@visactor/vutils": "~0.19.4"}}, "node_modules/@visactor/vgrammar-core": {"version": "0.16.3", "resolved": "https://registry.npmjs.org/@visactor/vgrammar-core/-/vgrammar-core-0.16.3.tgz", "integrity": "sha512-cd7hmh9JobbCDUJPOshmQB5V0KVM0GLTPBe/ZySJDi1cUSWpukAgRrLozEk/M5XgDbVIT+4pjqe6siacCad8dg==", "dependencies": {"@visactor/vdataset": "~0.19.4", "@visactor/vgrammar-coordinate": "0.16.3", "@visactor/vgrammar-util": "0.16.3", "@visactor/vrender-components": "0.22.6", "@visactor/vrender-core": "0.22.6", "@visactor/vrender-kits": "0.22.6", "@visactor/vscale": "~0.19.4", "@visactor/vutils": "~0.19.4"}}, "node_modules/@visactor/vgrammar-hierarchy": {"version": "0.16.3", "resolved": "https://registry.npmjs.org/@visactor/vgrammar-hierarchy/-/vgrammar-hierarchy-0.16.3.tgz", "integrity": "sha512-qnfSWRt1PErkVPTtet8DVc4MY+WwmgJoNNW2FALFht1qUfPdglTqT96drPbkurwiZMzSk+Xfr7+IPUA8ZQwWag==", "dependencies": {"@visactor/vgrammar-core": "0.16.3", "@visactor/vgrammar-util": "0.16.3", "@visactor/vrender-core": "0.22.6", "@visactor/vrender-kits": "0.22.6", "@visactor/vutils": "~0.19.4"}}, "node_modules/@visactor/vgrammar-projection": {"version": "0.16.3", "resolved": "https://registry.npmjs.org/@visactor/vgrammar-projection/-/vgrammar-projection-0.16.3.tgz", "integrity": "sha512-c+MJ3qgtsNQHwZCDBVT7fNahNxe0g827IiytQWvrtMxavLIrtJqeul5H+6BYrGvYk8d81ByxNZdoVNn/mfNtDw==", "dependencies": {"@visactor/vgrammar-core": "0.16.3", "@visactor/vgrammar-util": "0.16.3", "@visactor/vutils": "~0.19.4", "d3-geo": "^1.12.1"}}, "node_modules/@visactor/vgrammar-sankey": {"version": "0.16.3", "resolved": "https://registry.npmjs.org/@visactor/vgrammar-sankey/-/vgrammar-sankey-0.16.3.tgz", "integrity": "sha512-7j0xx77Yn2KzY4EcZ27qFF6R1KTcmy3BtQQewOHA1uoUX8ZRsfe57eziYRiBhyVrzdFWoa0IJqzH7Yk/zITvuQ==", "dependencies": {"@visactor/vgrammar-core": "0.16.3", "@visactor/vgrammar-util": "0.16.3", "@visactor/vrender-core": "0.22.6", "@visactor/vrender-kits": "0.22.6", "@visactor/vutils": "~0.19.4"}}, "node_modules/@visactor/vgrammar-util": {"version": "0.16.3", "resolved": "https://registry.npmjs.org/@visactor/vgrammar-util/-/vgrammar-util-0.16.3.tgz", "integrity": "sha512-aF9MqjTR7YvBAVDtp1A/CDVcXFGlO+TxkHVPEQVrn7cVu2DGRXCZnu/iQ+AUhttVYaWlSRflZj4cnQrKS4zy4g==", "dependencies": {"@visactor/vrender-core": "0.22.6", "@visactor/vutils": "~0.19.4"}}, "node_modules/@visactor/vgrammar-venn": {"version": "0.16.3", "resolved": "https://registry.npmjs.org/@visactor/vgrammar-venn/-/vgrammar-venn-0.16.3.tgz", "integrity": "sha512-M6mtCrpOcPrD6nkQFZ3Fl0Z2zPaKFTyRIPeO235vDwB/ZzefN5BObh85UGsv0swK46L5yu3daBxW0VtrGMBZRA==", "dependencies": {"@visactor/vgrammar-core": "0.16.3", "@visactor/vgrammar-util": "0.16.3", "@visactor/vrender-core": "0.22.6", "@visactor/vrender-kits": "0.22.6", "@visactor/vutils": "~0.19.4"}}, "node_modules/@visactor/vgrammar-wordcloud": {"version": "0.16.3", "resolved": "https://registry.npmjs.org/@visactor/vgrammar-wordcloud/-/vgrammar-wordcloud-0.16.3.tgz", "integrity": "sha512-uIHUJ3CGir+IjDjv4SpJR5SZvWSIYU2VoBdoCvFdhP9j8t15wadGYfe0/br9d6xOM3laiSCFYvPdhy0Ke5sP4w==", "dependencies": {"@visactor/vgrammar-core": "0.16.3", "@visactor/vgrammar-util": "0.16.3", "@visactor/vrender-core": "0.22.6", "@visactor/vrender-kits": "0.22.6", "@visactor/vutils": "~0.19.4"}}, "node_modules/@visactor/vgrammar-wordcloud-shape": {"version": "0.16.3", "resolved": "https://registry.npmjs.org/@visactor/vgrammar-wordcloud-shape/-/vgrammar-wordcloud-shape-0.16.3.tgz", "integrity": "sha512-ZWRHbec4WM2W3v2t57gRaX1IUGy+nDRjumcctgzSvmCpmR3nORgLKmMhxXYEA0VwcpY+umM0lVcd42iqPH8c7g==", "dependencies": {"@visactor/vgrammar-core": "0.16.3", "@visactor/vgrammar-util": "0.16.3", "@visactor/vrender-core": "0.22.6", "@visactor/vrender-kits": "0.22.6", "@visactor/vscale": "~0.19.4", "@visactor/vutils": "~0.19.4"}}, "node_modules/@visactor/vmind": {"version": "2.0.5", "resolved": "https://bnpm.byted.org/@visactor/vmind/-/vmind-2.0.5.tgz", "integrity": "sha512-QztQaeSkdeRZYOUlB4qaBpx3/swyO3JzFH8eYvSgvptS/rf8aQDZiufAUasafDLkcME5N6RpBGkcGYIDkmt74Q==", "dependencies": {"@stdlib/stats-base-dists-t-quantile": "0.2.1", "@visactor/calculator": "2.0.5", "@visactor/chart-advisor": "2.0.5", "@visactor/vchart-theme": "^1.11.2", "@visactor/vdataset": "~0.19.3", "@visactor/vutils": "~0.19.3", "alasql": "~4.3.2", "array-normalize": "~2.0.0", "axios": "^1.4.0", "bayesian-changepoint": "~1.0.1", "dayjs": "~1.11.10", "density-clustering": "~1.3.0", "euclidean-distance": "~1.0.0", "js-yaml": "~4.1.0", "json5": "~2.2.3", "jsonrepair": "~3.8.1", "jstat": "~1.9.6", "string-similarity-js": "~2.1.4"}}, "node_modules/@visactor/vrender-components": {"version": "0.22.6", "resolved": "https://registry.npmjs.org/@visactor/vrender-components/-/vrender-components-0.22.6.tgz", "integrity": "sha512-YHLjA2GzP5LQxAAgzo2iniBxDldy9GtEzjm/sCXrrOGzzwMFlyhAeXCbUVEIMhTTfpRdK5LocAP1PSJsv4BObA==", "dependencies": {"@visactor/vrender-core": "0.22.6", "@visactor/vrender-kits": "0.22.6", "@visactor/vscale": "~0.19.4", "@visactor/vutils": "~0.19.4"}}, "node_modules/@visactor/vrender-core": {"version": "0.22.6", "resolved": "https://registry.npmjs.org/@visactor/vrender-core/-/vrender-core-0.22.6.tgz", "integrity": "sha512-R/MPjAuF9vT5atn7tAqhA5K1FMqYzv2SOhREsJpgP6QbJSnGR2uMTrNENRFvrM81ikR6yeh7WeTx6Fh2av+M4A==", "dependencies": {"@visactor/vutils": "~0.19.4", "color-convert": "2.0.1"}}, "node_modules/@visactor/vrender-kits": {"version": "0.22.6", "resolved": "https://registry.npmjs.org/@visactor/vrender-kits/-/vrender-kits-0.22.6.tgz", "integrity": "sha512-0yRvhMhnT3JeFKCOi8riubkuKjNMIlzcW1FQV+kIyOGGV6nCSjvFL4+XDuEGalHlHt76BSlM1/cmxnmRNTHCRQ==", "dependencies": {"@resvg/resvg-js": "2.4.1", "@visactor/vrender-core": "0.22.6", "@visactor/vutils": "~0.19.4", "gifuct-js": "2.1.2", "lottie-web": "^5.12.2", "roughjs": "4.5.2"}}, "node_modules/@visactor/vscale": {"version": "0.19.4", "resolved": "https://registry.npmjs.org/@visactor/vscale/-/vscale-0.19.4.tgz", "integrity": "sha512-kp69hPMof3GBKRuUiXSR9+9K+Z8ZXsTlOAwcnknXmiiZDhdcDkPlv27/d+Xx1Wi/iqw+BS2S7YIjHmfzdiVQ/Q==", "dependencies": {"@visactor/vutils": "0.19.4"}}, "node_modules/@visactor/vutils": {"version": "0.19.4", "resolved": "https://registry.npmjs.org/@visactor/vutils/-/vutils-0.19.4.tgz", "integrity": "sha512-kLbcsTe1/3HSSvEJvJikzGD0plY0gdHbpxt98oo7W6OrianfYd97nm/w7rFXcq/S49e6C5d1SdU4MZk/PYxhEQ==", "dependencies": {"@turf/helpers": "^6.5.0", "@turf/invariant": "^6.5.0", "eventemitter3": "^4.0.7"}}, "node_modules/@visactor/vutils-extension": {"version": "1.13.8", "resolved": "https://registry.npmjs.org/@visactor/vutils-extension/-/vutils-extension-1.13.8.tgz", "integrity": "sha512-mOtUJjUEthQTHyYnynWJs8wbbW+UoW0z18lH++TqGoDbsJLcr4Mlpxhe8IDP/bda7kRVTI/FHbzVHhKWKLBvxw==", "dependencies": {"@visactor/vdataset": "~0.19.4", "@visactor/vutils": "~0.19.4"}}, "node_modules/abbrev": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/abbrev/-/abbrev-1.1.1.tgz", "integrity": "sha512-nne9/IiQ/hzIhY6pdDnbBtz7DjPTKrY00P/zvPSm5pOFkl6xuGrGnXn/VtTNNfNtAfZ9/1RtehkszU9qcTii0Q=="}, "node_modules/abs-svg-path": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/abs-svg-path/-/abs-svg-path-0.1.1.tgz", "integrity": "sha512-d8XPSGjfyzlXC3Xx891DJRyZfqk5JU0BJrDQcsWomFIV1/BIzPW5HDH5iDdWpqWaav0YVIEzT1RHTwWr0FFshA=="}, "node_modules/acorn": {"version": "8.14.1", "resolved": "https://registry.npmjs.org/acorn/-/acorn-8.14.1.tgz", "integrity": "sha512-OvQ/2pUDKmgfCg++xsTX1wGxfTaszcHVcTctW4UJB4hibJx2HXxxO5UmVgyjMa+ZDsiaf5wWLXYpRWMmBI0QHg==", "dev": true, "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/acorn-walk": {"version": "8.3.4", "resolved": "https://registry.npmjs.org/acorn-walk/-/acorn-walk-8.3.4.tgz", "integrity": "sha512-ueEepnujpqee2o5aIYnvHU6C0A42MNdsIDeqy5BydrkuC5R1ZuUFnm27EeFJGoEHJQgn3uleRvmTXaJgfXbt4g==", "dev": true, "dependencies": {"acorn": "^8.11.0"}, "engines": {"node": ">=0.4.0"}}, "node_modules/agent-base": {"version": "6.0.2", "resolved": "https://registry.npmjs.org/agent-base/-/agent-base-6.0.2.tgz", "integrity": "sha512-RZNwNclF7+MS/8bDg70amg32dyeZGZxiDuQmZxKLAlQjr3jGyLx+4Kkk58UO7D2QdgFIQCovuSuZESne6RG6XQ==", "dependencies": {"debug": "4"}, "engines": {"node": ">= 6.0.0"}}, "node_modules/agent-base/node_modules/debug": {"version": "4.4.0", "resolved": "https://registry.npmjs.org/debug/-/debug-4.4.0.tgz", "integrity": "sha512-6WTZ/IxCY/T6BALoZHaE4ctp9xm+Z5kY/pzYaCHRFeyVhojxlrm+46y68HA6hr0TcwEssoxNiDEUJQjfPZ/RYA==", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/agent-base/node_modules/ms": {"version": "2.1.3", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA=="}, "node_modules/alasql": {"version": "4.3.3", "resolved": "https://registry.npmjs.org/alasql/-/alasql-4.3.3.tgz", "integrity": "sha512-IP64TOG+zBTPA41OB2NJVkM3urEIhvZtYwtPFC/1QSH7nCzwShIwWfxwyOhTK7yzF/ZaNGEpc3Eexyzb2nUbFg==", "dependencies": {"cross-fetch": "4", "yargs": "16"}, "bin": {"alasql": "bin/alasql-cli.js"}, "engines": {"node": ">=15"}}, "node_modules/ansi-regex": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz", "integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==", "engines": {"node": ">=8"}}, "node_modules/ansi-styles": {"version": "4.3.0", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz", "integrity": "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/aproba": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/aproba/-/aproba-2.0.0.tgz", "integrity": "sha512-lYe4Gx7QT+MKGbDsA+Z+he/Wtef0BiwDOlK/XkBrdfsh9J/jPPXbX0tE9x9cl27Tmu5gg3QUbUrQYa/y+KOHPQ=="}, "node_modules/are-we-there-yet": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/are-we-there-yet/-/are-we-there-yet-2.0.0.tgz", "integrity": "sha512-Ci/qENmwHnsYo9xKIcUJN5LeDKdJ6R1Z1j9V/J5wyq8nh/mYPEpIKJbBZXtZjG04HiK7zV/p6Vs9952MrMeUIw==", "deprecated": "This package is no longer supported.", "dependencies": {"delegates": "^1.0.0", "readable-stream": "^3.6.0"}, "engines": {"node": ">=10"}}, "node_modules/arg": {"version": "4.1.3", "resolved": "https://registry.npmjs.org/arg/-/arg-4.1.3.tgz", "integrity": "sha512-58S9QDqG0Xx27YwPSt9fJxivjYl432YCwfDMfZ+71RAqUrZef7LrKQZ3LHLOwCS4FLNBplP533Zx895SeOCHvA==", "dev": true}, "node_modules/argparse": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz", "integrity": "sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q=="}, "node_modules/array-bounds": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/array-bounds/-/array-bounds-1.0.1.tgz", "integrity": "sha512-8wdW3ZGk6UjMPJx/glyEt0sLzzwAE1bhToPsO1W2pbpR2gULyxe3BjSiuJFheP50T/GgODVPz2fuMUmIywt8cQ=="}, "node_modules/array-normalize": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/array-normalize/-/array-normalize-2.0.0.tgz", "integrity": "sha512-WofPolGg9OqpmfYh2qqOJ0yeJ9Idjn+EcQ+Nyy3eQbqtuz0MRyqTEHB0PH/Ypp2PpsOAfjsqTMzu1fHOaPzO1Q==", "dependencies": {"array-bounds": "^1.0.0"}}, "node_modules/array-source": {"version": "0.0.4", "resolved": "https://registry.npmjs.org/array-source/-/array-source-0.0.4.tgz", "integrity": "sha512-frNdc+zBn80vipY+GdcJkLEbMWj3xmzArYApmUGxoiV8uAu/ygcs9icPdsGdA26h0MkHUMW6EN2piIvVx+M5Mw=="}, "node_modules/asynckit": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz", "integrity": "sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q=="}, "node_modules/axios": {"version": "1.8.4", "resolved": "https://registry.npmjs.org/axios/-/axios-1.8.4.tgz", "integrity": "sha512-eBSYY4Y68NNlHbHBMdeDmKNtDgXWhQsJcGqzO3iLUM0GraQFSS9cVgPX5I9b3lbdFKyYoAEGAZF1DwhTaljNAw==", "dependencies": {"follow-redirects": "^1.15.6", "form-data": "^4.0.0", "proxy-from-env": "^1.1.0"}}, "node_modules/balanced-match": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz", "integrity": "sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw=="}, "node_modules/bayesian-changepoint": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/bayesian-changepoint/-/bayesian-changepoint-1.0.1.tgz", "integrity": "sha512-OhSHWfGiEcBtI46b5guJGmj6pJEjvyaXsRPCAQy5MPoVaDZ38poXmzVZLSIuw6VLQmZs58+uf5F9iFA4NVmTTA=="}, "node_modules/big-integer": {"version": "1.6.52", "resolved": "https://bnpm.byted.org/big-integer/-/big-integer-1.6.52.tgz", "integrity": "sha512-QxD8cf2eVqJOOz63z6JIN9BzvVs/dlySa5HGSBH5xtR8dPteIRQnBxxKqkNTiT6jbDTF6jAfrd4oMcND9RGbQg==", "engines": {"node": ">=0.6"}}, "node_modules/brace-expansion": {"version": "1.1.11", "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz", "integrity": "sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/buffer-from": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.2.tgz", "integrity": "sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ=="}, "node_modules/call-bind-apply-helpers": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz", "integrity": "sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/canvas": {"version": "2.11.2", "resolved": "https://registry.npmjs.org/canvas/-/canvas-2.11.2.tgz", "integrity": "sha512-ItanGBMrmRV7Py2Z+Xhs7cT+FNt5K0vPL4p9EZ/UX/Mu7hFbkxSjKF2KVtPwX7UYWp7dRKnrTvReflgrItJbdw==", "hasInstallScript": true, "dependencies": {"@mapbox/node-pre-gyp": "^1.0.0", "nan": "^2.17.0", "simple-get": "^3.0.3"}, "engines": {"node": ">=6"}}, "node_modules/chownr": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/chownr/-/chownr-2.0.0.tgz", "integrity": "sha512-bIomtDF5KGpdogkLd9VspvFzk9KfpyyGlS8YFVZl7TGPBHL5snIOnxeshwVgPteQ9b4Eydl+pVbIyE1DcvCWgQ==", "engines": {"node": ">=10"}}, "node_modules/cliui": {"version": "7.0.4", "resolved": "https://registry.npmjs.org/cliui/-/cliui-7.0.4.tgz", "integrity": "sha512-OcRE68cOsVMXp1Yvonl/fzkQOyjLSu/8bhPDfQt0e0/Eb283TKP20Fs2MqoPsr9SwA595rRCA+QMzYc9nBP+JQ==", "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.0", "wrap-ansi": "^7.0.0"}}, "node_modules/color-convert": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz", "integrity": "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "resolved": "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz", "integrity": "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA=="}, "node_modules/color-support": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/color-support/-/color-support-1.1.3.tgz", "integrity": "sha512-qiBjkpbMLO/HL68y+lh4q0/O1MZFj2RX6X/KmMa3+gJD3z+WwI1ZzDHysvqHGS3mP6mznPckpXmw1nI9cJjyRg==", "bin": {"color-support": "bin.js"}}, "node_modules/combined-stream": {"version": "1.0.8", "resolved": "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz", "integrity": "sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==", "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/commander": {"version": "2.20.3", "resolved": "https://registry.npmjs.org/commander/-/commander-2.20.3.tgz", "integrity": "sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ=="}, "node_modules/concat-map": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz", "integrity": "sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg=="}, "node_modules/concat-stream": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/concat-stream/-/concat-stream-2.0.0.tgz", "integrity": "sha512-MWufYdFw53ccGjCA+Ol7XJYpAlW6/prSMzuPOTRnJGcGzuhLn4Scrz7qf6o8bROZ514ltazcIFJZevcfbo0x7A==", "engines": ["node >= 6.0"], "dependencies": {"buffer-from": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^3.0.2", "typedarray": "^0.0.6"}}, "node_modules/console-control-strings": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/console-control-strings/-/console-control-strings-1.1.0.tgz", "integrity": "sha512-ty/fTekppD2fIwRvnZAVdeOiGd1c7YXEixbgJTNzqcxJWKQnjJ/V1bNEEE6hygpM3WjwHFUVK6HTjWSzV4a8sQ=="}, "node_modules/core-util-is": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.3.tgz", "integrity": "sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ=="}, "node_modules/create-require": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/create-require/-/create-require-1.1.1.tgz", "integrity": "sha512-dcKFX3jn0MpIaXjisoRvexIJVEKzaq7z2rZKxf+MSr9TkdmHmsU4m2lcLojrj/FHl8mk5VxMmYA+ftRkP/3oKQ==", "dev": true}, "node_modules/cross-fetch": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-4.1.0.tgz", "integrity": "sha512-uKm5PU+MHTootlWEY+mZ4vvXoCn4fLQxT9dSc1sXVMSFkINTJVN8cAQROpwcKm8bJ/c7rgZVIBWzH5T78sNZZw==", "dependencies": {"node-fetch": "^2.7.0"}}, "node_modules/d3-array": {"version": "1.2.4", "resolved": "https://registry.npmjs.org/d3-array/-/d3-array-1.2.4.tgz", "integrity": "sha512-KHW6M86R+FUPYGb3R5XiYjXPq7VzwxZ22buHhAEVG5ztoEcZZMLov530mmccaqA1GghZArjQV46fuc8kUqhhHw=="}, "node_modules/d3-dsv": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/d3-dsv/-/d3-dsv-2.0.0.tgz", "integrity": "sha512-E+Pn8UJYx9mViuIUkoc93gJGGYut6mSDKy2+XaPwccwkRGlR+LO97L2VCCRjQivTwLHkSnAJG7yo00BWY6QM+w==", "dependencies": {"commander": "2", "iconv-lite": "0.4", "rw": "1"}, "bin": {"csv2json": "bin/dsv2json", "csv2tsv": "bin/dsv2dsv", "dsv2dsv": "bin/dsv2dsv", "dsv2json": "bin/dsv2json", "json2csv": "bin/json2dsv", "json2dsv": "bin/json2dsv", "json2tsv": "bin/json2dsv", "tsv2csv": "bin/dsv2dsv", "tsv2json": "bin/dsv2json"}}, "node_modules/d3-geo": {"version": "1.12.1", "resolved": "https://registry.npmjs.org/d3-geo/-/d3-geo-1.12.1.tgz", "integrity": "sha512-XG4d1c/UJSEX9NfU02KwBL6BYPj8YKHxgBEw5om2ZnTRSbIcego6dhHwcxuSR3clxh0EpE38os1DVPOmnYtTPg==", "dependencies": {"d3-array": "1"}}, "node_modules/d3-hexbin": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/d3-hexbin/-/d3-hexbin-0.2.2.tgz", "integrity": "sha512-KS3fUT2ReD4RlGCjvCEm1RgMtp2NFZumdMu4DBzQK8AZv3fXRM6Xm8I4fSU07UXvH4xxg03NwWKWdvxfS/yc4w=="}, "node_modules/d3-hierarchy": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/d3-hierarchy/-/d3-hierarchy-3.1.2.tgz", "integrity": "sha512-FX/9frcub54beBdugHjDCdikxThEqjnR93Qt7PvQTOHxyiNCAlvMrHhclk3cD5VeAaq9fxmfRp+CnWw9rEMBuA==", "engines": {"node": ">=12"}}, "node_modules/dayjs": {"version": "1.11.13", "resolved": "https://registry.npmjs.org/dayjs/-/dayjs-1.11.13.tgz", "integrity": "sha512-oaMBel6gjolK862uaPQOVTA7q3TZhuSvuMQAAglQDOWYO9A91IrAOUJEyKVlqJlHE0vq5p5UXxzdPfMH/x6xNg=="}, "node_modules/debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "dependencies": {"ms": "2.0.0"}}, "node_modules/decompress-response": {"version": "4.2.1", "resolved": "https://registry.npmjs.org/decompress-response/-/decompress-response-4.2.1.tgz", "integrity": "sha512-jOSne2qbyE+/r8G1VU+G/82LBs2Fs4LAsTiLSHOCOMZQl2OKZ6i8i4IyHemTe+/yIXOtTcRQMzPcgyhoFlqPkw==", "dependencies": {"mimic-response": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/delayed-stream": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz", "integrity": "sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==", "engines": {"node": ">=0.4.0"}}, "node_modules/delegates": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/delegates/-/delegates-1.0.0.tgz", "integrity": "sha512-bd2L678uiWATM6m5Z1VzNCErI3jiGzt6HGY8OVICs40JQq/HALfbyNJmp0UDakEY4pMMaN0Ly5om/B1VI/+xfQ=="}, "node_modules/density-clustering": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/density-clustering/-/density-clustering-1.3.0.tgz", "integrity": "sha512-icpmBubVTwLnsaor9qH/4tG5+7+f61VcqMN3V3pm9sxxSCt2Jcs0zWOgwZW9ARJYaKD3FumIgHiMOcIMRRAzFQ=="}, "node_modules/detect-libc": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/detect-libc/-/detect-libc-2.0.3.tgz", "integrity": "sha512-bwy0MGW55bG41VqxxypOsdSdGqLwXPI/focwgTYCFMbdUiBAxLg9CFzG08sz2aqzknwiX7Hkl0bQENjg8iLByw==", "engines": {"node": ">=8"}}, "node_modules/diff": {"version": "4.0.2", "resolved": "https://registry.npmjs.org/diff/-/diff-4.0.2.tgz", "integrity": "sha512-58lmxKSA4BNyLz+HHMUzlOEpg09FV+ev6ZMe3vJihgdxzgcwZ8VoEEPmALCZG9LmqfVoNMMKpttIYTVG6uDY7A==", "dev": true, "engines": {"node": ">=0.3.1"}}, "node_modules/dunder-proto": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/dunder-proto/-/dunder-proto-1.0.1.tgz", "integrity": "sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==", "dependencies": {"call-bind-apply-helpers": "^1.0.1", "es-errors": "^1.3.0", "gopd": "^1.2.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/emoji-regex": {"version": "8.0.0", "resolved": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz", "integrity": "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A=="}, "node_modules/es-define-property": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.1.tgz", "integrity": "sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==", "engines": {"node": ">= 0.4"}}, "node_modules/es-errors": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/es-errors/-/es-errors-1.3.0.tgz", "integrity": "sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==", "engines": {"node": ">= 0.4"}}, "node_modules/es-object-atoms": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.1.1.tgz", "integrity": "sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==", "dependencies": {"es-errors": "^1.3.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-set-tostringtag": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/es-set-tostringtag/-/es-set-tostringtag-2.1.0.tgz", "integrity": "sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA==", "dependencies": {"es-errors": "^1.3.0", "get-intrinsic": "^1.2.6", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/escalade": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz", "integrity": "sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==", "engines": {"node": ">=6"}}, "node_modules/euclidean-distance": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/euclidean-distance/-/euclidean-distance-1.0.0.tgz", "integrity": "sha512-3+1fOi9GKT2PhSX+uKZ/cX4F98wLY2gTibZPPZeToEPvHZNLnnoymcJgQzWeeIMvqciQRIhn9KEKY7QVplC7hQ=="}, "node_modules/eventemitter3": {"version": "4.0.7", "resolved": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-4.0.7.tgz", "integrity": "sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw=="}, "node_modules/file-source": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/file-source/-/file-source-0.6.1.tgz", "integrity": "sha512-1R1KneL7eTXmXfKxC10V/9NeGOdbsAXJ+lQ//fvvcHUgtaZcZDWNJNblxAoVOyV1cj45pOtUrR3vZTBwqcW8XA==", "dependencies": {"stream-source": "0.3"}}, "node_modules/follow-redirects": {"version": "1.15.9", "resolved": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.9.tgz", "integrity": "sha512-gew4GsXizNgdoRyqmyfMHyAmXsZDk6mHkSxZFCzW9gwlbtOW44CDtYavM+y+72qD/Vq2l550kMF52DT8fOLJqQ==", "funding": [{"type": "individual", "url": "https://github.com/sponsors/Ruben<PERSON>"}], "engines": {"node": ">=4.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}}, "node_modules/form-data": {"version": "4.0.2", "resolved": "https://registry.npmjs.org/form-data/-/form-data-4.0.2.tgz", "integrity": "sha512-hGfm/slu0ZabnNt4oaRZ6uREyfCj6P4fT/n6A1rGV+Z0VdGXjfOhVUpkn6qVQONHGIFwmveGXyDs75+nr6FM8w==", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "es-set-tostringtag": "^2.1.0", "mime-types": "^2.1.12"}, "engines": {"node": ">= 6"}}, "node_modules/fs-minipass": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/fs-minipass/-/fs-minipass-2.1.0.tgz", "integrity": "sha512-V/JgOLFCS+R6Vcq0slCuaeWEdNC3ouDlJMNIsacH2VtALiu9mV4LPrHc5cDl8k5aw6J8jwgWWpiTo5RYhmIzvg==", "dependencies": {"minipass": "^3.0.0"}, "engines": {"node": ">= 8"}}, "node_modules/fs-minipass/node_modules/minipass": {"version": "3.3.6", "resolved": "https://registry.npmjs.org/minipass/-/minipass-3.3.6.tgz", "integrity": "sha512-DxiNidxSEK+tHG6zOIklvNOwm3hvCrbUrdtzY74U6HKTJxvIDfOUL5W5P2Ghd3DTkhhKPYGqeNUIh5qcM4YBfw==", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/fs.realpath": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz", "integrity": "sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw=="}, "node_modules/function-bind": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz", "integrity": "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/gauge": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/gauge/-/gauge-3.0.2.tgz", "integrity": "sha512-+5J6MS/5XksCuXq++uFRsnUd7Ovu1XenbeuIuNRJxYWjgQbPuFhT14lAvsWfqfAmnwluf1OwMjz39HjfLPci0Q==", "deprecated": "This package is no longer supported.", "dependencies": {"aproba": "^1.0.3 || ^2.0.0", "color-support": "^1.1.2", "console-control-strings": "^1.0.0", "has-unicode": "^2.0.1", "object-assign": "^4.1.1", "signal-exit": "^3.0.0", "string-width": "^4.2.3", "strip-ansi": "^6.0.1", "wide-align": "^1.1.2"}, "engines": {"node": ">=10"}}, "node_modules/geobuf": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/geobuf/-/geobuf-3.0.2.tgz", "integrity": "sha512-ASgKwEAQQRnyNFHNvpd5uAwstbVYmiTW0Caw3fBb509tNTqXyAAPMyFs5NNihsLZhLxU1j/kjFhkhLWA9djuVg==", "dependencies": {"concat-stream": "^2.0.0", "pbf": "^3.2.1", "shapefile": "~0.6.6"}, "bin": {"geobuf2json": "bin/geobuf2json", "json2geobuf": "bin/json2geobuf", "shp2geobuf": "bin/shp2geobuf"}}, "node_modules/geojson-dissolve": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/geojson-dissolve/-/geojson-dissolve-3.1.0.tgz", "integrity": "sha512-JXHfn+A3tU392HA703gJbjmuHaQOAE/C1KzbELCczFRFux+GdY6zt1nKb1VMBHp4LWeE7gUY2ql+g06vJqhiwQ==", "dependencies": {"@turf/meta": "^3.7.5", "geojson-flatten": "^0.2.1", "geojson-linestring-dissolve": "0.0.1", "topojson-client": "^3.0.0", "topojson-server": "^3.0.0"}}, "node_modules/geojson-dissolve/node_modules/@turf/meta": {"version": "3.14.0", "resolved": "https://registry.npmjs.org/@turf/meta/-/meta-3.14.0.tgz", "integrity": "sha512-OtXqLQuR9hlQ/HkAF/OdzRea7E0eZK1ay8y8CBXkoO2R6v34CsDrWYLMSo0ZzMsaQDpKo76NPP2GGo+PyG1cSg=="}, "node_modules/geojson-flatten": {"version": "0.2.4", "resolved": "https://registry.npmjs.org/geojson-flatten/-/geojson-flatten-0.2.4.tgz", "integrity": "sha512-LiX6Jmot8adiIdZ/fthbcKKPOfWjTQchX/ggHnwMZ2e4b0I243N1ANUos0LvnzepTEsj0+D4fIJ5bKhBrWnAHA==", "dependencies": {"get-stdin": "^6.0.0", "minimist": "1.2.0"}, "bin": {"geojson-flatten": "geo<PERSON><PERSON>-flatten"}}, "node_modules/geojson-flatten/node_modules/get-stdin": {"version": "6.0.0", "resolved": "https://registry.npmjs.org/get-stdin/-/get-stdin-6.0.0.tgz", "integrity": "sha512-jp4tHawyV7+fkkSKyvjuLZswblUtz+SQKzSWnBbii16BuZksJlU1wuBYXY75r+duh/llF1ur6oNwi+2ZzjKZ7g==", "engines": {"node": ">=4"}}, "node_modules/geojson-linestring-dissolve": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/geojson-linestring-dissolve/-/geojson-linestring-dissolve-0.0.1.tgz", "integrity": "sha512-Y8I2/Ea28R/Xeki7msBcpMvJL2TaPfaPKP8xqueJfQ9/jEhps+iOJxOR2XCBGgVb12Z6XnDb1CMbaPfLepsLaw=="}, "node_modules/get-caller-file": {"version": "2.0.5", "resolved": "https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz", "integrity": "sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==", "engines": {"node": "6.* || 8.* || >= 10.*"}}, "node_modules/get-intrinsic": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.3.0.tgz", "integrity": "sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "function-bind": "^1.1.2", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "math-intrinsics": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-proto": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/get-proto/-/get-proto-1.0.1.tgz", "integrity": "sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==", "dependencies": {"dunder-proto": "^1.0.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/get-stdin": {"version": "9.0.0", "resolved": "https://registry.npmjs.org/get-stdin/-/get-stdin-9.0.0.tgz", "integrity": "sha512-dVKBjfWisLAicarI2Sf+JuBE/DghV4UzNAVe9yhEJuzeREd3JhOTE9cUaJTeSa77fsbQUK3pcOpJfM59+VKZaA==", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/gifuct-js": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/gifuct-js/-/gifuct-js-2.1.2.tgz", "integrity": "sha512-rI2asw77u0mGgwhV3qA+OEgYqaDn5UNqgs+Bx0FGwSpuqfYn+Ir6RQY5ENNQ8SbIiG/m5gVa7CD5RriO4f4Lsg==", "dependencies": {"js-binary-schema-parser": "^2.0.3"}}, "node_modules/glob": {"version": "7.2.3", "resolved": "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz", "integrity": "sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==", "deprecated": "Glob versions prior to v9 are no longer supported", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/gopd": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/gopd/-/gopd-1.2.0.tgz", "integrity": "sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-symbols": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/has-symbols/-/has-symbols-1.1.0.tgz", "integrity": "sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-tostringtag": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/has-tostringtag/-/has-tostringtag-1.0.2.tgz", "integrity": "sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==", "dependencies": {"has-symbols": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-unicode": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/has-unicode/-/has-unicode-2.0.1.tgz", "integrity": "sha512-8Rf9Y83NBReMnx0gFzA8JImQACstCYWUplepDa9xprwwtmgEZUF0h/i5xSA625zB/I37EtrswSST6OXxwaaIJQ=="}, "node_modules/hasown": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz", "integrity": "sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/https-proxy-agent": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-5.0.1.tgz", "integrity": "sha512-dFcAjpTQFgoLMzC2VwU+C/CbS7uRL0lWmxDITmqm7C+7F0Odmj6s9l6alZc6AELXhrnggM2CeWSXHGOdX2YtwA==", "dependencies": {"agent-base": "6", "debug": "4"}, "engines": {"node": ">= 6"}}, "node_modules/https-proxy-agent/node_modules/debug": {"version": "4.4.0", "resolved": "https://registry.npmjs.org/debug/-/debug-4.4.0.tgz", "integrity": "sha512-6WTZ/IxCY/T6BALoZHaE4ctp9xm+Z5kY/pzYaCHRFeyVhojxlrm+46y68HA6hr0TcwEssoxNiDEUJQjfPZ/RYA==", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/https-proxy-agent/node_modules/ms": {"version": "2.1.3", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA=="}, "node_modules/iconv-lite": {"version": "0.4.24", "resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.24.tgz", "integrity": "sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==", "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/ieee754": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/ieee754/-/ieee754-1.2.1.tgz", "integrity": "sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}, "node_modules/inflight": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz", "integrity": "sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==", "deprecated": "This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.", "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/inherits": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "integrity": "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ=="}, "node_modules/is-core-module": {"version": "2.16.1", "resolved": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.16.1.tgz", "integrity": "sha512-UfoeMA6fIJ8wTYFEUjelnaGI67v6+N7qXJEvQuIGa99l4xsCruSYOVSQ0uPANn4dAzm8lkYPaKLrrijLq7x23w==", "dependencies": {"hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-fullwidth-code-point": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==", "engines": {"node": ">=8"}}, "node_modules/isarray": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/isarray/-/isarray-0.0.1.tgz", "integrity": "sha512-D2S+3GLxWH+uhrNEcoh/fnmYeP8E8/zHl644d/jdA0g2uyXvy3sb0qxotE+ne0LtccHknQzWwZEzhak7oJ0COQ=="}, "node_modules/js-binary-schema-parser": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/js-binary-schema-parser/-/js-binary-schema-parser-2.0.3.tgz", "integrity": "sha512-xezGJmOb4lk/M1ZZLTR/jaBHQ4gG/lqQnJqdIv4721DMggsa1bDVlHXNeHYogaIEHD9vCRv0fcL4hMA+Coarkg=="}, "node_modules/js-yaml": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz", "integrity": "sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/json5": {"version": "2.2.3", "resolved": "https://registry.npmjs.org/json5/-/json5-2.2.3.tgz", "integrity": "sha512-<PERSON>m<PERSON>e7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==", "bin": {"json5": "lib/cli.js"}, "engines": {"node": ">=6"}}, "node_modules/jsonrepair": {"version": "3.8.1", "resolved": "https://registry.npmjs.org/jsonrepair/-/jsonrepair-3.8.1.tgz", "integrity": "sha512-5wnjaO53EJOhfLFY92nvBz2B9gqF9ql/D4HKUb1WOSBaqtVcAifFfmurblnhCJn/ySqKFA8U3n7nhGMAu/hEjQ==", "bin": {"jsonrepair": "bin/cli.js"}}, "node_modules/jstat": {"version": "1.9.6", "resolved": "https://registry.npmjs.org/jstat/-/jstat-1.9.6.tgz", "integrity": "sha512-rPBkJbK2TnA8pzs93QcDDPlKcrtZWuuCo2dVR0TFLOJSxhqfWOVCSp8aV3/oSbn+4uY4yw1URtLpHQedtmXfug=="}, "node_modules/lottie-web": {"version": "5.12.2", "resolved": "https://registry.npmjs.org/lottie-web/-/lottie-web-5.12.2.tgz", "integrity": "sha512-uvhvYPC8kGPjXT3MyKMrL3JitEAmDMp30lVkuq/590Mw9ok6pWcFCwXJveo0t5uqYw1UREQHofD+jVpdjBv8wg=="}, "node_modules/make-dir": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/make-dir/-/make-dir-3.1.0.tgz", "integrity": "sha512-g3FeP20LNwhALb/6Cz6Dd4F2ngze0jz7tbzrD2wAV+o9FeNHe4rL+yK2md0J/fiSf1sa1ADhXqi5+oVwOM/eGw==", "dependencies": {"semver": "^6.0.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/make-dir/node_modules/semver": {"version": "6.3.1", "resolved": "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz", "integrity": "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==", "bin": {"semver": "bin/semver.js"}}, "node_modules/make-error": {"version": "1.3.6", "resolved": "https://registry.npmjs.org/make-error/-/make-error-1.3.6.tgz", "integrity": "sha512-s8UhlNe7vPKomQhC1qFelMokr/Sc3AgNbso3n74mVPA5LTZwkB9NlXf4XPamLxJE8h0gh73rM94xvwRT2CVInw==", "dev": true}, "node_modules/math-intrinsics": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/math-intrinsics/-/math-intrinsics-1.1.0.tgz", "integrity": "sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==", "engines": {"node": ">= 0.4"}}, "node_modules/mime-db": {"version": "1.52.0", "resolved": "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz", "integrity": "sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.35", "resolved": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz", "integrity": "sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/mimic-response": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/mimic-response/-/mimic-response-2.1.0.tgz", "integrity": "sha512-wXqjST+SLt7R009ySCglWBCFpjUygmCIfD790/kVbiGmUgfYGuB14PiTd5DwVxSV4NcYHjzMkoj5LjQZwTQLEA==", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/minimatch": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/minimist": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/minimist/-/minimist-1.2.0.tgz", "integrity": "sha512-7Wl+Jz+IGWuSdgsQEJ4JunV0si/iMhg42MnQQG6h1R6TNeVenp4U9x5CC5v/gYqz/fENLQITAWXidNtVL0NNbw=="}, "node_modules/minipass": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/minipass/-/minipass-5.0.0.tgz", "integrity": "sha512-3FnjYuehv9k6ovOEbyOswadCDPX1piCfhV8ncmYtHOjuPwylVWsghTLo7rabjC3Rx5xD4HDx8Wm1xnMF7S5qFQ==", "engines": {"node": ">=8"}}, "node_modules/minizlib": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/minizlib/-/minizlib-2.1.2.tgz", "integrity": "sha512-bAxsR8BVfj60DWXHE3u30oHzfl4G7khkSuPW+qvpd7jFRHm7dLxOjUk1EHACJ/hxLY8phGJ0YhYHZo7jil7Qdg==", "dependencies": {"minipass": "^3.0.0", "yallist": "^4.0.0"}, "engines": {"node": ">= 8"}}, "node_modules/minizlib/node_modules/minipass": {"version": "3.3.6", "resolved": "https://registry.npmjs.org/minipass/-/minipass-3.3.6.tgz", "integrity": "sha512-DxiNidxSEK+tHG6zOIklvNOwm3hvCrbUrdtzY74U6HKTJxvIDfOUL5W5P2Ghd3DTkhhKPYGqeNUIh5qcM4YBfw==", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/mkdirp": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/mkdirp/-/mkdirp-1.0.4.tgz", "integrity": "sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==", "bin": {"mkdirp": "bin/cmd.js"}, "engines": {"node": ">=10"}}, "node_modules/ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A=="}, "node_modules/nan": {"version": "2.22.2", "resolved": "https://registry.npmjs.org/nan/-/nan-2.22.2.tgz", "integrity": "sha512-DANghxFkS1plDdRsX0X9pm0Z6SJNN6gBdtXfanwoZ8hooC5gosGFSBGRYHUVPz1asKA/kMRqDRdHrluZ61SpBQ=="}, "node_modules/node-fetch": {"version": "2.7.0", "resolved": "https://registry.npmjs.org/node-fetch/-/node-fetch-2.7.0.tgz", "integrity": "sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==", "dependencies": {"whatwg-url": "^5.0.0"}, "engines": {"node": "4.x || >=6.0.0"}, "peerDependencies": {"encoding": "^0.1.0"}, "peerDependenciesMeta": {"encoding": {"optional": true}}}, "node_modules/node-sql-parser": {"version": "4.17.0", "resolved": "https://bnpm.byted.org/node-sql-parser/-/node-sql-parser-4.17.0.tgz", "integrity": "sha512-3IhovpmUBpcETnoKK/KBdkz2mz53kVG5E1dnqz1QuYvtzdxYZW5xaGGEvW9u6Yyy2ivwR3eUZrn9inmEVef02w==", "dependencies": {"big-integer": "^1.6.48"}, "engines": {"node": ">=8"}}, "node_modules/nopt": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/nopt/-/nopt-5.0.0.tgz", "integrity": "sha512-Tbj67rffqceeLpcRXrT7vKAN8CwfPeIBgM7E6iBkmKLV7bEMwpGgYLGv0jACUsECaa/vuxP0IjEont6umdMgtQ==", "dependencies": {"abbrev": "1"}, "bin": {"nopt": "bin/nopt.js"}, "engines": {"node": ">=6"}}, "node_modules/npmlog": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/npmlog/-/npmlog-5.0.1.tgz", "integrity": "sha512-AqZtDUWOMKs1G/8lwylVjrdYgqA4d9nu8hc+0gzRxlDb1I10+FHBGMXs6aiQHFdCUUlqH99MUMuLfzWDNDtfxw==", "deprecated": "This package is no longer supported.", "dependencies": {"are-we-there-yet": "^2.0.0", "console-control-strings": "^1.1.0", "gauge": "^3.0.0", "set-blocking": "^2.0.0"}}, "node_modules/object-assign": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==", "engines": {"node": ">=0.10.0"}}, "node_modules/once": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/once/-/once-1.4.0.tgz", "integrity": "sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==", "dependencies": {"wrappy": "1"}}, "node_modules/parse-svg-path": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/parse-svg-path/-/parse-svg-path-0.1.2.tgz", "integrity": "sha512-JyPSBnkTJ0AI8GGJLfMXvKq42cj5c006fnLz6fXy6zfoVjJizi8BNTpu8on8ziI1cKy9d9DGNuY17Ce7wuejpQ=="}, "node_modules/path-browserify": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/path-browserify/-/path-browserify-1.0.1.tgz", "integrity": "sha512-b7uo2UCUOYZcnF/3ID0lulOJi/bafxa1xPe7ZPsammBSpjSWQkjNxlt635YGS2MiR9GjvuXCtz2emr3jbsz98g=="}, "node_modules/path-data-parser": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/path-data-parser/-/path-data-parser-0.1.0.tgz", "integrity": "sha512-NOnmBpt5Y2RWbuv0LMzsayp3lVylAHLPUTut412ZA3l+C4uw4ZVkQbjShYCQ8TCpUMdPapr4YjUqLYD6v68j+w=="}, "node_modules/path-is-absolute": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "integrity": "sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==", "engines": {"node": ">=0.10.0"}}, "node_modules/path-parse": {"version": "1.0.7", "resolved": "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz", "integrity": "sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw=="}, "node_modules/path-source": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/path-source/-/path-source-0.1.3.tgz", "integrity": "sha512-dWRHm5mIw5kw0cs3QZLNmpUWty48f5+5v9nWD2dw3Y0Hf+s01Ag8iJEWV0Sm0kocE8kK27DrIowha03e1YR+Qw==", "dependencies": {"array-source": "0.0", "file-source": "0.6"}}, "node_modules/pbf": {"version": "3.3.0", "resolved": "https://registry.npmjs.org/pbf/-/pbf-3.3.0.tgz", "integrity": "sha512-XDF38WCH3z5OV/OVa8GKUNtLAyneuzbCisx7QUCF8Q6Nutx0WnJrQe5O+kOtBlLfRNUws98Y58Lblp+NJG5T4Q==", "dependencies": {"ieee754": "^1.1.12", "resolve-protobuf-schema": "^2.1.0"}, "bin": {"pbf": "bin/pbf"}}, "node_modules/point-at-length": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/point-at-length/-/point-at-length-1.1.0.tgz", "integrity": "sha512-nNHDk9rNEh/91o2Y8kHLzBLNpLf80RYd2gCun9ss+V0ytRSf6XhryBTx071fesktjbachRmGuUbId+JQmzhRXw==", "dependencies": {"abs-svg-path": "~0.1.1", "isarray": "~0.0.1", "parse-svg-path": "~0.1.1"}}, "node_modules/points-on-curve": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/points-on-curve/-/points-on-curve-0.2.0.tgz", "integrity": "sha512-0mYKnYYe9ZcqMCWhUjItv/oHjvgEsfKvnUTg8sAtnHr3GVy7rGkXCb6d5cSyqrWqL4k81b9CPg3urd+T7aop3A=="}, "node_modules/points-on-path": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/points-on-path/-/points-on-path-0.2.1.tgz", "integrity": "sha512-25ClnWWuw7JbWZcgqY/gJ4FQWadKxGWk+3kR/7kD0tCaDtPPMj7oHu2ToLaVhfpnHrZzYby2w6tUA0eOIuUg8g==", "dependencies": {"path-data-parser": "0.1.0", "points-on-curve": "0.2.0"}}, "node_modules/protocol-buffers-schema": {"version": "3.6.0", "resolved": "https://registry.npmjs.org/protocol-buffers-schema/-/protocol-buffers-schema-3.6.0.tgz", "integrity": "sha512-TdDRD+/QNdrCGCE7v8340QyuXd4kIWIgapsE2+n/SaGiSSbomYl4TjHlvIoCWRpE7wFt02EpB35VVA2ImcBVqw=="}, "node_modules/proxy-from-env": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/proxy-from-env/-/proxy-from-env-1.1.0.tgz", "integrity": "sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg=="}, "node_modules/readable-stream": {"version": "3.6.2", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.2.tgz", "integrity": "sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/require-directory": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz", "integrity": "sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==", "engines": {"node": ">=0.10.0"}}, "node_modules/resolve": {"version": "1.22.10", "resolved": "https://registry.npmjs.org/resolve/-/resolve-1.22.10.tgz", "integrity": "sha512-NPRy+/ncIMeDlTAsuqwKIiferiawhefFJtkNSW0qZJEqMEb+qBt/77B/jGeeek+F0uOeN05CDa6HXbbIgtVX4w==", "dependencies": {"is-core-module": "^2.16.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/resolve-protobuf-schema": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/resolve-protobuf-schema/-/resolve-protobuf-schema-2.1.0.tgz", "integrity": "sha512-kI5ffTiZWmJaS/huM8wZfEMer1eRd7oJQhDuxeCLe3t7N7mX3z94CN0xPxBQxFYQTSNz9T0i+v6inKqSdK8xrQ==", "dependencies": {"protocol-buffers-schema": "^3.3.1"}}, "node_modules/rimraf": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/rimraf/-/rimraf-3.0.2.tgz", "integrity": "sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==", "deprecated": "Rimraf versions prior to v4 are no longer supported", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/roughjs": {"version": "4.5.2", "resolved": "https://registry.npmjs.org/roughjs/-/roughjs-4.5.2.tgz", "integrity": "sha512-2xSlLDKdsWyFxrveYWk9YQ/Y9UfK38EAMRNkYkMqYBJvPX8abCa9PN0x3w02H8Oa6/0bcZICJU+U95VumPqseg==", "dependencies": {"path-data-parser": "^0.1.0", "points-on-curve": "^0.2.0", "points-on-path": "^0.2.1"}}, "node_modules/rw": {"version": "1.3.3", "resolved": "https://registry.npmjs.org/rw/-/rw-1.3.3.tgz", "integrity": "sha512-PdhdWy89SiZogBLaw42zdeqtRJ//zFd2PgQavcICDUgJT5oW10QCRKbJ6bg4r0/UY2M6BWd5tkxuGFRvCkgfHQ=="}, "node_modules/safe-buffer": {"version": "5.2.1", "resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}, "node_modules/safer-buffer": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz", "integrity": "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg=="}, "node_modules/semver": {"version": "7.7.1", "resolved": "https://registry.npmjs.org/semver/-/semver-7.7.1.tgz", "integrity": "sha512-hlq8tAfn0m/61p4BVRcPzIGr6LKiMwo4VM6dGi6pt4qcRkmNzTcWq6eCEjEh+qXjkMDvPlOFFSGwQjoEa6gyMA==", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/set-blocking": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/set-blocking/-/set-blocking-2.0.0.tgz", "integrity": "sha512-KiKBS8AnWGEyLzofFfmvKwpdPzqiy16LvQfK3yv/fVH7Bj13/wl3JSR1J+rfgRE9q7xUJK4qvgS8raSOeLUehw=="}, "node_modules/shapefile": {"version": "0.6.6", "resolved": "https://registry.npmjs.org/shapefile/-/shapefile-0.6.6.tgz", "integrity": "sha512-rLGSWeK2ufzCVx05wYd+xrWnOOdSV7xNUW5/XFgx3Bc02hBkpMlrd2F1dDII7/jhWzv0MSyBFh5uJIy9hLdfuw==", "dependencies": {"array-source": "0.0", "commander": "2", "path-source": "0.1", "slice-source": "0.4", "stream-source": "0.3", "text-encoding": "^0.6.4"}, "bin": {"dbf2json": "bin/dbf2json", "shp2json": "bin/shp2json"}}, "node_modules/signal-exit": {"version": "3.0.7", "resolved": "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz", "integrity": "sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ=="}, "node_modules/simple-concat": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/simple-concat/-/simple-concat-1.0.1.tgz", "integrity": "sha512-cSFtAPtRhljv69IK0hTVZQ+OfE9nePi/rtJmw5UjHeVyVroEqJXP1sFztKUy1qU+xvz3u/sfYJLa947b7nAN2Q==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}, "node_modules/simple-get": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/simple-get/-/simple-get-3.1.1.tgz", "integrity": "sha512-CQ5LTKGfCpvE1K0n2us+kuMPbk/q0EKl82s4aheV9oXjFEz6W/Y7oQFVJuU6QG77hRT4Ghb5RURteF5vnWjupA==", "dependencies": {"decompress-response": "^4.2.0", "once": "^1.3.1", "simple-concat": "^1.0.0"}}, "node_modules/simple-statistics": {"version": "7.8.8", "resolved": "https://registry.npmjs.org/simple-statistics/-/simple-statistics-7.8.8.tgz", "integrity": "sha512-CUtP0+uZbcbsFpqEyvNDYjJCl+612fNgjT8GaVuvMG7tBuJg8gXGpsP5M7X658zy0IcepWOZ6nPBu1Qb9ezA1w==", "engines": {"node": "*"}}, "node_modules/simplify-geojson": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/simplify-geojson/-/simplify-geojson-1.0.5.tgz", "integrity": "sha512-02l1W4UipP5ivNVq6kX15mAzCRIV1oI3tz0FUEyOsNiv1ltuFDjbNhO+nbv/xhbDEtKqWLYuzpWhUsJrjR/ypA==", "dependencies": {"concat-stream": "~1.4.1", "minimist": "1.2.6", "simplify-geometry": "0.0.2"}, "bin": {"simplify-geojson": "cli.js"}}, "node_modules/simplify-geojson/node_modules/concat-stream": {"version": "1.4.11", "resolved": "https://registry.npmjs.org/concat-stream/-/concat-stream-1.4.11.tgz", "integrity": "sha512-X3JMh8+4je3U1cQpG87+f9lXHDrqcb2MVLg9L7o8b1UZ0DzhRrUpdn65ttzu10PpJPPI3MQNkis+oha6TSA9Mw==", "engines": ["node >= 0.8"], "dependencies": {"inherits": "~2.0.1", "readable-stream": "~1.1.9", "typedarray": "~0.0.5"}}, "node_modules/simplify-geojson/node_modules/minimist": {"version": "1.2.6", "resolved": "https://registry.npmjs.org/minimist/-/minimist-1.2.6.tgz", "integrity": "sha512-Jsjnk4bw3YJqYzbdyBiNsPWHPfO++UGG749Cxs6peCu5Xg4nrena6OVxOYxrQTqww0Jmwt+Ref8rggumkTLz9Q=="}, "node_modules/simplify-geojson/node_modules/readable-stream": {"version": "1.1.14", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.1.14.tgz", "integrity": "sha512-+MeVjFf4L44XUkhM1eYbD8fyEsxcV81pqMSR5gblfcLCHfZvbrqy4/qYHE+/R5HoBUT11WV5O08Cr1n3YXkWVQ==", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "node_modules/simplify-geojson/node_modules/string_decoder": {"version": "0.10.31", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-0.10.31.tgz", "integrity": "sha512-ev2QzSzWPYmy9GuqfIVildA4OdcGLeFZQrq5ys6RtiuF+RQQiZWr8TZNyAcuVXyQRYfEO+MsoB/1BuQVhOJuoQ=="}, "node_modules/simplify-geometry": {"version": "0.0.2", "resolved": "https://registry.npmjs.org/simplify-geometry/-/simplify-geometry-0.0.2.tgz", "integrity": "sha512-ZEyrplkqgCqDlL7V8GbbYgTLlcnNF+MWWUdy8s8ZeJru50bnI71rDew/I+HG36QS2mPOYAq1ZjwNXxHJ8XOVBw=="}, "node_modules/slice-source": {"version": "0.4.1", "resolved": "https://registry.npmjs.org/slice-source/-/slice-source-0.4.1.tgz", "integrity": "sha512-YiuPbxpCj4hD9Qs06hGAz/OZhQ0eDuALN0lRWJez0eD/RevzKqGdUx1IOMUnXgpr+sXZLq3g8ERwbAH0bCb8vg=="}, "node_modules/stream-source": {"version": "0.3.5", "resolved": "https://registry.npmjs.org/stream-source/-/stream-source-0.3.5.tgz", "integrity": "sha512-ZuEDP9sgjiAwUVoDModftG0JtYiLUV8K4ljYD1VyUMRWtbVf92474o4kuuul43iZ8t/hRuiDAx1dIJSvirrK/g=="}, "node_modules/string_decoder": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.3.0.tgz", "integrity": "sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==", "dependencies": {"safe-buffer": "~5.2.0"}}, "node_modules/string-similarity-js": {"version": "2.1.4", "resolved": "https://registry.npmjs.org/string-similarity-js/-/string-similarity-js-2.1.4.tgz", "integrity": "sha512-uApODZNjCHGYROzDSAdCmAHf60L/pMDHnP/yk6TAbvGg7JSPZlSto/ceCI7hZEqzc53/juU2aOJFkM2yUVTMTA=="}, "node_modules/string-width": {"version": "4.2.3", "resolved": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz", "integrity": "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-ansi": {"version": "6.0.1", "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz", "integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/supports-preserve-symlinks-flag": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz", "integrity": "sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/tar": {"version": "6.2.1", "resolved": "https://registry.npmjs.org/tar/-/tar-6.2.1.tgz", "integrity": "sha512-DZ4yORTwrbTj/7MZYq2w+/ZFdI6OZ/f9SFHR+71gIVUZhOQPHzVCLpvRnPgyaMpfWxxk/4ONva3GQSyNIKRv6A==", "dependencies": {"chownr": "^2.0.0", "fs-minipass": "^2.0.0", "minipass": "^5.0.0", "minizlib": "^2.1.1", "mkdirp": "^1.0.3", "yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/text-encoding": {"version": "0.6.4", "resolved": "https://registry.npmjs.org/text-encoding/-/text-encoding-0.6.4.tgz", "integrity": "sha512-hJnc6Qg3dWoOMkqP53F0dzRIgtmsAge09kxUIqGrEUS4qr5rWLckGYaQAVr+opBrIMRErGgy6f5aPnyPpyGRfg==", "deprecated": "no longer maintained"}, "node_modules/topojson-client": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/topojson-client/-/topojson-client-3.1.0.tgz", "integrity": "sha512-605uxS6bcYxGXw9qi62XyrV6Q3xwbndjachmNxu8HWTtVPxZfEJN9fd/SZS1Q54Sn2y0TMyMxFj/cJINqGHrKw==", "dependencies": {"commander": "2"}, "bin": {"topo2geo": "bin/topo2geo", "topomerge": "bin/topomerge", "topoquantize": "bin/topoquantize"}}, "node_modules/topojson-server": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/topojson-server/-/topojson-server-3.0.1.tgz", "integrity": "sha512-/VS9j/ffKr2XAOjlZ9CgyyeLmgJ9dMwq6Y0YEON8O7p/tGGk+dCWnrE03zEdu7i4L7YsFZLEPZPzCvcB7lEEXw==", "dependencies": {"commander": "2"}, "bin": {"geo2topo": "bin/geo2topo"}}, "node_modules/tr46": {"version": "0.0.3", "resolved": "https://registry.npmjs.org/tr46/-/tr46-0.0.3.tgz", "integrity": "sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw=="}, "node_modules/ts-node": {"version": "10.9.2", "resolved": "https://registry.npmjs.org/ts-node/-/ts-node-10.9.2.tgz", "integrity": "sha512-f0FFpIdcHgn8zcPSbf1dRevwt047YMnaiJM3u2w2RewrB+fob/zePZcrOyQoLMMO7aBIddLcQIEK5dYjkLnGrQ==", "dev": true, "dependencies": {"@cspotcode/source-map-support": "^0.8.0", "@tsconfig/node10": "^1.0.7", "@tsconfig/node12": "^1.0.7", "@tsconfig/node14": "^1.0.0", "@tsconfig/node16": "^1.0.2", "acorn": "^8.4.1", "acorn-walk": "^8.1.1", "arg": "^4.1.0", "create-require": "^1.1.0", "diff": "^4.0.1", "make-error": "^1.1.1", "v8-compile-cache-lib": "^3.0.1", "yn": "3.1.1"}, "bin": {"ts-node": "dist/bin.js", "ts-node-cwd": "dist/bin-cwd.js", "ts-node-esm": "dist/bin-esm.js", "ts-node-script": "dist/bin-script.js", "ts-node-transpile-only": "dist/bin-transpile.js", "ts-script": "dist/bin-script-deprecated.js"}, "peerDependencies": {"@swc/core": ">=1.2.50", "@swc/wasm": ">=1.2.50", "@types/node": "*", "typescript": ">=2.7"}, "peerDependenciesMeta": {"@swc/core": {"optional": true}, "@swc/wasm": {"optional": true}}}, "node_modules/ts-pattern": {"version": "4.1.4", "resolved": "https://bnpm.byted.org/ts-pattern/-/ts-pattern-4.1.4.tgz", "integrity": "sha512-Mcw65oUd1w5ktKi5BRwrnz16Otwk9iv7P0dKgvbi+A1albCDgnixohSqNLuFwIp5dzxPmTPm0iDQ6p1ZJr9uGw=="}, "node_modules/typedarray": {"version": "0.0.6", "resolved": "https://registry.npmjs.org/typedarray/-/typedarray-0.0.6.tgz", "integrity": "sha512-/aCDEGatGvZ2BIk+HmLf4ifCJFwvKFNb9/JeZPMulfgFracn9QFcAf5GO8B/mweUjSoblS5In0cWhqpfs/5PQA=="}, "node_modules/typescript": {"version": "5.8.2", "resolved": "https://registry.npmjs.org/typescript/-/typescript-5.8.2.tgz", "integrity": "sha512-aJn6wq13/afZp/jT9QZmwEjDqqvSGp1VT5GVg+f/t6/oVyrgXM6BY1h9BRh/O5p3PlUPAe+WuiEZOmb/49RqoQ==", "dev": true, "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=14.17"}}, "node_modules/undici-types": {"version": "6.20.0", "resolved": "https://registry.npmjs.org/undici-types/-/undici-types-6.20.0.tgz", "integrity": "sha512-Ny6QZ2Nju20vw1SRHe3d9jVu6gJ+4e3+MMpqu7pqE5HT6WsTSlce++GQmK5UXS8mzV8DSYHrQH+Xrf2jVcuKNg==", "dev": true}, "node_modules/util-deprecate": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz", "integrity": "sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw=="}, "node_modules/v8-compile-cache-lib": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/v8-compile-cache-lib/-/v8-compile-cache-lib-3.0.1.tgz", "integrity": "sha512-wa7YjyUGfNZngI/vtK0UHAN+lgDCxBPCylVXGp0zu59Fz5aiGtNXaq3DhIov063MorB+VfufLh3JlF2KdTK3xg==", "dev": true}, "node_modules/webidl-conversions": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-3.0.1.tgz", "integrity": "sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ=="}, "node_modules/whatwg-url": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-5.0.0.tgz", "integrity": "sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==", "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}}, "node_modules/wide-align": {"version": "1.1.5", "resolved": "https://registry.npmjs.org/wide-align/-/wide-align-1.1.5.tgz", "integrity": "sha512-eDMORYaPNZ4sQIuuYPDHdQvf4gyCF9rEEV/yPxGfwPkRodwEgiMUUXTx/dex+Me0wxx53S+NgUHaP7y3MGlDmg==", "dependencies": {"string-width": "^1.0.2 || 2 || 3 || 4"}}, "node_modules/wrap-ansi": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "integrity": "sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/wrappy": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz", "integrity": "sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ=="}, "node_modules/y18n": {"version": "5.0.8", "resolved": "https://registry.npmjs.org/y18n/-/y18n-5.0.8.tgz", "integrity": "sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==", "engines": {"node": ">=10"}}, "node_modules/yallist": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/yallist/-/yallist-4.0.0.tgz", "integrity": "sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A=="}, "node_modules/yargs": {"version": "16.2.0", "resolved": "https://registry.npmjs.org/yargs/-/yargs-16.2.0.tgz", "integrity": "sha512-D1mvvtDG0L5ft/jGWkLpG1+m0eQxOfaBvTNELraWj22wSVUMWxZUvYgJYcKh6jGGIkJFhH4IZPQhR4TKpc8mBw==", "dependencies": {"cliui": "^7.0.2", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.0", "y18n": "^5.0.5", "yargs-parser": "^20.2.2"}, "engines": {"node": ">=10"}}, "node_modules/yargs-parser": {"version": "20.2.9", "resolved": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-20.2.9.tgz", "integrity": "sha512-y11nGElTIV+CT3Zv9t7VKl+Q3hTQoT9a1Qzezhhl6Rp21gJ/IVTW7Z3y9EWXhuUBC2Shnf+DX0antecpAwSP8w==", "engines": {"node": ">=10"}}, "node_modules/yn": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/yn/-/yn-3.1.1.tgz", "integrity": "sha512-Ux4ygGWsu2c7isFWe8Yu1YluJmqVhxqK2cLXNQA5AcC3QfbGNpM7fu0Y8b/z16pXLnFxZYvWhd3fhBY9DLmC6Q==", "dev": true, "engines": {"node": ">=6"}}}}