#!/usr/bin/env python3
"""
Basic test script to check if core functionality works without browser dependencies.
"""

import asyncio
import sys

# Test basic imports
try:
    from app.config import config
    print("✓ Config import successful")
except Exception as e:
    print(f"✗ Config import failed: {e}")
    sys.exit(1)

try:
    from app.llm import LLM
    print("✓ LLM import successful")
except Exception as e:
    print(f"✗ LLM import failed: {e}")
    sys.exit(1)

async def test_basic_functionality():
    """Test basic functionality without browser dependencies."""
    try:
        # Test config loading
        print(f"Config loaded: {config.llm['default'].model}")

        # Test LLM initialization
        llm = LLM()
        print("✓ LLM initialized successfully")

        # Test a simple message (this will fail without proper API key, but should not crash)
        try:
            messages = [{"role": "user", "content": "Hello, this is a test."}]
            # This will likely fail due to invalid API key, but we're testing the structure
            response = await llm.ask(messages, timeout=5)
            print("✓ LLM ask method works")
        except Exception as e:
            print(f"⚠ LLM ask failed (expected with demo key): {e}")

        print("✓ Basic functionality test completed")

    except Exception as e:
        print(f"✗ Basic functionality test failed: {e}")
        return False

    return True

if __name__ == "__main__":
    print("Testing basic OpenManus functionality...")
    print(f"Python version: {sys.version}")

    result = asyncio.run(test_basic_functionality())
    if result:
        print("\n✓ Basic tests passed! Core functionality is working.")
    else:
        print("\n✗ Basic tests failed.")
