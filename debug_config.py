#!/usr/bin/env python3
"""
Debug script to check config loading.
"""

try:
    from app.config import config
    print("Config object:", config)
    print("Config type:", type(config))
    print("Config attributes:", dir(config))
    
    if hasattr(config, 'llm'):
        print("LLM config:", config.llm)
        print("LLM type:", type(config.llm))
    else:
        print("No 'llm' attribute found")
        
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
